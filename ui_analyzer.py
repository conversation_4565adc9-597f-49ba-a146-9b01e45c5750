import json
from typing import Dict, List, Any, Optional, Tu<PERSON>, Set
import chromadb
from sentence_transformers import SentenceTransformer
import google.generativeai as genai
from config import Config
import re
from difflib import SequenceMatcher
import os
from system_message_processor import SystemMessageProcessor
from heuristic_evaluator import HeuristicEvaluator

class UIElementProcessor:
    """Enhanced processor for UI elements with comprehensive vector database storage"""

    def __init__(self):
        self.config = Config()
        # Set Hugging Face token for model downloads
        import os
        os.environ["HUGGINGFACE_HUB_TOKEN"] = self.config.HUGGINGFACE_TOKEN

        try:
            self.embeddings = SentenceTransformer(
                self.config.EMBEDDING_MODEL,
                token=self.config.HUGGINGFACE_TOKEN
            )
            print("✅ SentenceTransformer loaded successfully!")
        except Exception as e:
            # Fallback to a simpler approach without embeddings
            print(f"Warning: Could not load SentenceTransformer: {e}. Using simple text matching.")
            self.embeddings = None

        self.chroma_client = chromadb.PersistentClient(path=self.config.CHROMA_DB_PATH)
        self.coordinates_collection = None
        self.dom_collection = None
        self._initialize_collections()

        # Store raw data for index-based retrieval
        self.coordinates_data = []
        self.element_info_data = {}

        # Load controlled vocabulary from label.txt
        self.controlled_vocabulary = self._load_controlled_vocabulary()

    def _initialize_collections(self):
        """Initialize or get existing ChromaDB collections for coordinates and DOM data"""
        # Initialize coordinates collection
        try:
            self.coordinates_collection = self.chroma_client.get_collection(name="ui_coordinates")
            print("✅ Loaded existing coordinates collection")
        except:
            self.coordinates_collection = self.chroma_client.create_collection(
                name="ui_coordinates",
                metadata={"description": "UI element coordinates and labels for semantic search"}
            )
            print("✅ Created new coordinates collection")

        # Initialize DOM collection
        try:
            self.dom_collection = self.chroma_client.get_collection(name="ui_dom_data")
            print("✅ Loaded existing DOM collection")
        except:
            self.dom_collection = self.chroma_client.create_collection(
                name="ui_dom_data",
                metadata={"description": "Complete DOM element information indexed by position"}
            )
            print("✅ Created new DOM collection")

    def _load_controlled_vocabulary(self) -> Set[str]:
        """Load controlled vocabulary from label.txt file for accurate keyword extraction"""
        vocabulary = set()
        labels_path = self.config.DEFAULT_LABELS_PATH

        try:
            if os.path.exists(labels_path):
                with open(labels_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        label = line.strip()
                        if label:  # Skip empty lines
                            vocabulary.add(label.lower())  # Store in lowercase for matching
                            # Also add individual words from multi-word labels
                            words = label.lower().split()
                            for word in words:
                                if len(word) > 2:  # Only add meaningful words
                                    vocabulary.add(word)

                print(f"✅ Loaded {len(vocabulary)} terms from controlled vocabulary")
                return vocabulary
            else:
                print(f"⚠️ Label file not found at {labels_path}. Using fallback keyword extraction.")
                return set()
        except Exception as e:
            print(f"⚠️ Error loading controlled vocabulary: {e}. Using fallback keyword extraction.")
            return set()

    def process_and_store_data(self, coordinates_path: str, element_info_path: str):
        """Enhanced processing and storage of UI element data in separate vector databases"""
        # Load coordinates data
        with open(coordinates_path, 'r') as f:
            self.coordinates_data = json.load(f)

        # Load element info data
        with open(element_info_path, 'r') as f:
            self.element_info_data = json.load(f)

        # Clear existing data
        try:
            self.coordinates_collection.delete()
            self.dom_collection.delete()
        except:
            pass

        # Store coordinates data for semantic search
        self._store_coordinates_data()

        # Store complete DOM data indexed by position
        self._store_dom_data()

        print(f"✅ Stored {len(self.coordinates_data)} coordinate elements and {len(self.element_info_data)} DOM elements")

    def _store_coordinates_data(self):
        """Store coordinates data in vector database for semantic search"""
        coord_documents = []
        coord_metadatas = []
        coord_ids = []

        for i, coord_item in enumerate(self.coordinates_data):
            # Create searchable text for coordinates
            coord_text = self._create_coordinate_search_text(coord_item, i)

            # Create metadata for coordinates
            coord_metadata = {
                "index": i,
                "element_id": f"element_{i+1}",
                "label": coord_item["label"],
                "x": coord_item["coordinates"]["x"],
                "y": coord_item["coordinates"]["y"],
                "width": coord_item["coordinates"]["width"],
                "height": coord_item["coordinates"]["height"]
            }

            coord_documents.append(coord_text)
            coord_metadatas.append(coord_metadata)
            coord_ids.append(f"coord_{i}")

        # Store coordinates in ChromaDB
        if coord_documents:
            self.coordinates_collection.add(
                documents=coord_documents,
                metadatas=coord_metadatas,
                ids=coord_ids
            )
            print(f"✅ Stored {len(coord_documents)} coordinate elements for semantic search")

    def _store_dom_data(self):
        """Store complete DOM data indexed by position"""
        dom_documents = []
        dom_metadatas = []
        dom_ids = []

        for element_key, element_info in self.element_info_data.items():
            # Extract index from element_key (e.g., "element_1" -> 0)
            index = int(element_key.split('_')[1]) - 1

            # Create comprehensive DOM text for embedding
            dom_text = self._create_dom_search_text(element_info, index)

            # Create comprehensive metadata including all DOM information
            # Note: ChromaDB doesn't accept None values, so we convert them to empty strings
            dom_metadata = {
                "index": index,
                "element_id": element_key,
                "tag": element_info.get("tag") or "",
                "text": element_info.get("text") or "",
                "css_selector": element_info.get("cssSelector") or "",
                "xpath": element_info.get("xpath") or "",
                "src": element_info.get("src") or "",
                "href": element_info.get("href") or "",
                "classes": json.dumps(element_info.get("classes", [])),
                "attributes": json.dumps(element_info.get("attributes", {})),
                "computed_style_keys": json.dumps(list(element_info.get("computedStyle", {}).keys())[:50]),  # Limit for metadata
                # Store the entire element_info as JSON for complete retrieval
                "full_element_data": json.dumps(element_info),
                # Add coordinate information if available
                "has_coordinates": index < len(self.coordinates_data),
                "coordinate_label": self.coordinates_data[index]["label"] if index < len(self.coordinates_data) else "",
                "coordinate_x": self.coordinates_data[index]["coordinates"]["x"] if index < len(self.coordinates_data) else 0,
                "coordinate_y": self.coordinates_data[index]["coordinates"]["y"] if index < len(self.coordinates_data) else 0,
                "coordinate_width": self.coordinates_data[index]["coordinates"]["width"] if index < len(self.coordinates_data) else 0,
                "coordinate_height": self.coordinates_data[index]["coordinates"]["height"] if index < len(self.coordinates_data) else 0
            }

            dom_documents.append(dom_text)
            dom_metadatas.append(dom_metadata)
            dom_ids.append(f"dom_{index}")

        # Store DOM data in ChromaDB
        if dom_documents:
            self.dom_collection.add(
                documents=dom_documents,
                metadatas=dom_metadatas,
                ids=dom_ids
            )
            print(f"✅ Stored {len(dom_documents)} DOM elements with complete information")

    def _create_coordinate_search_text(self, coord_item: Dict, index: int) -> str:
        """Create searchable text for coordinate data"""
        text_parts = [
            f"Label: {coord_item['label']}",
            f"Element type: {coord_item['label'].lower()}",
            f"UI element: {coord_item['label']}",
            f"Position: x={coord_item['coordinates']['x']}, y={coord_item['coordinates']['y']}",
            f"Size: {coord_item['coordinates']['width']}x{coord_item['coordinates']['height']}",
            f"Index: {index}",
            f"Element number: {index + 1}"
        ]
        return " | ".join(text_parts)

    def _create_dom_search_text(self, element_info: Dict, index: int) -> str:
        """Create comprehensive searchable text for DOM data"""
        text_parts = [
            f"Tag: {element_info.get('tag', '')}",
            f"Text content: {element_info.get('text', '')}",
            f"CSS classes: {' '.join(element_info.get('classes', []))}",
            f"CSS selector: {element_info.get('cssSelector', '')}",
            f"XPath: {element_info.get('xpath', '')}",
            f"Index: {index}",
            f"Element number: {index + 1}"
        ]

        # Add attributes if available
        if 'attributes' in element_info:
            attrs = element_info['attributes']
            if attrs:
                attr_text = ", ".join([f"{k}={v}" for k, v in attrs.items() if v])
                text_parts.append(f"Attributes: {attr_text}")

        # Add some computed style information for better searchability
        if 'computedStyle' in element_info:
            computed_style = element_info['computedStyle']
            # Include key style properties that might be relevant for search
            relevant_styles = ['display', 'position', 'width', 'height', 'color', 'background-color', 'font-size']
            style_parts = []
            for style in relevant_styles:
                if style in computed_style:
                    style_parts.append(f"{style}: {computed_style[style]}")
            if style_parts:
                text_parts.append(f"Key styles: {', '.join(style_parts)}")

        return " | ".join(text_parts)

    def analyze_query_and_retrieve_context(self, query: str, top_k: int = None) -> Dict[str, Any]:
        """
        Enhanced query analysis following the specified workflow:
        1. Query Understanding: Detect keywords
        2. Vector Search in Coordinates DB: Search for labels
        3. Index Extraction: Identify indices
        4. DOM Data Retrieval: Get corresponding DOM data
        5. Context Injection: Combine coordinate and DOM information
        """
        if top_k is None:
            top_k = self.config.TOP_K_RESULTS

        # Step 1: Query Understanding - Extract keywords
        keywords = self._extract_keywords(query)
        print(f"🔍 Detected keywords: {keywords}")

        # Step 2: Vector Search in Coordinates DB
        coordinate_results = self._search_coordinates(query, top_k)
        print(f"📍 Found {len(coordinate_results)} relevant coordinate matches")

        # Step 3: Index Extraction
        relevant_indices = self._extract_indices_from_coordinates(coordinate_results)
        print(f"📊 Extracted indices: {relevant_indices}")

        # Step 4: DOM Data Retrieval using indices
        dom_context = self._retrieve_dom_data_by_indices(relevant_indices)
        print(f"🏗️ Retrieved DOM data for {len(dom_context)} elements")

        # Step 5: Context Injection - Combine coordinate and DOM information
        combined_context = self._combine_coordinate_and_dom_context(coordinate_results, dom_context)

        return {
            "keywords": keywords,
            "coordinate_matches": coordinate_results,
            "relevant_indices": relevant_indices,
            "dom_context": dom_context,
            "combined_context": combined_context,
            "query": query
        }

    def _extract_keywords(self, query: str) -> List[str]:
        """
        Enhanced keyword extraction using controlled vocabulary from label.txt
        Intelligently identifies the target UI element label the user is asking about
        """
        if not self.controlled_vocabulary:
            # Fallback to original method if no controlled vocabulary
            return self._extract_keywords_fallback(query)

        query_lower = query.lower()

        # Step 1: Identify the target UI element label
        target_label = self._identify_target_label(query_lower)

        if target_label:
            print(f"🎯 Target UI element identified: '{target_label}'")
            return [target_label]

        # Step 2: Fallback to general keyword extraction if no specific target found
        matched_keywords = self._extract_general_keywords(query_lower)

        print(f"🔍 Final matched keywords from controlled vocabulary: {matched_keywords}")
        return matched_keywords

    def _identify_target_label(self, query_lower: str) -> str:
        """
        Intelligently identify the specific UI element label the user is asking about
        """
        # Define query patterns that indicate the user is asking about a specific element
        element_patterns = [
            r'what is the (\w+)',
            r'where is the (\w+)',
            r'show me the (\w+)',
            r'find the (\w+)',
            r'tell me about the (\w+)',
            r'(\w+) element',
            r'the (\w+) on the page',
            r'(\w+) attributes',
            r'(\w+) properties',
            r'css classes for the (\w+)',
            r'xpath for the (\w+)',
        ]

        # Try to extract the target element from query patterns
        for pattern in element_patterns:
            matches = re.findall(pattern, query_lower)
            for match in matches:
                # Check if this match exists in our controlled vocabulary
                if match in self.controlled_vocabulary:
                    return match

                # Check for fuzzy matches
                fuzzy_match = self._find_best_fuzzy_match(match)
                if fuzzy_match:
                    print(f"🔍 Fuzzy match: '{match}' → '{fuzzy_match}'")
                    return fuzzy_match

        # Try to find multi-word labels that appear in the query
        for label in self.controlled_vocabulary:
            if len(label.split()) > 1:  # Multi-word labels
                if label in query_lower:
                    return label

        return None

    def _find_best_fuzzy_match(self, word: str) -> str:
        """Find the best fuzzy match for a word in the controlled vocabulary"""
        if len(word) < 3:  # Skip very short words
            return None

        best_match = None
        best_ratio = 0.0

        for vocab_term in self.controlled_vocabulary:
            # Only consider single words for fuzzy matching
            if len(vocab_term.split()) == 1:
                ratio = SequenceMatcher(None, word, vocab_term).ratio()
                if ratio > best_ratio and ratio > 0.8:  # 80% similarity threshold
                    best_ratio = ratio
                    best_match = vocab_term

        return best_match

    def _extract_general_keywords(self, query_lower: str) -> List[str]:
        """Extract general keywords when no specific target is identified"""
        matched_keywords = []

        # Find individual word matches
        words = re.findall(r'\b\w+\b', query_lower)
        for word in words:
            if word in self.controlled_vocabulary and word not in matched_keywords:
                matched_keywords.append(word)
                print(f"🎯 Vocabulary match found: '{word}'")

        # If no exact matches, try fuzzy matching
        if not matched_keywords:
            matched_keywords = self._fuzzy_match_keywords(query_lower)

        # Remove duplicates while preserving order
        unique_keywords = []
        for keyword in matched_keywords:
            if keyword not in unique_keywords:
                unique_keywords.append(keyword)

        return unique_keywords

    def _fuzzy_match_keywords(self, query_lower: str) -> List[str]:
        """Perform fuzzy matching against controlled vocabulary for better accuracy"""
        fuzzy_matches = []
        words = re.findall(r'\b\w+\b', query_lower)

        for word in words:
            if len(word) > 3:  # Only fuzzy match longer words
                best_match = None
                best_ratio = 0.0

                for vocab_term in self.controlled_vocabulary:
                    # Calculate similarity ratio
                    ratio = SequenceMatcher(None, word, vocab_term).ratio()
                    if ratio > best_ratio and ratio > 0.8:  # 80% similarity threshold
                        best_ratio = ratio
                        best_match = vocab_term

                if best_match:
                    fuzzy_matches.append(best_match)
                    print(f"🔍 Fuzzy match: '{word}' → '{best_match}' (similarity: {best_ratio:.2f})")

        return fuzzy_matches

    def _extract_keywords_fallback(self, query: str) -> List[str]:
        """Fallback keyword extraction method when controlled vocabulary is not available"""
        # Convert to lowercase and split
        words = re.findall(r'\b\w+\b', query.lower())

        # Filter out common stop words
        stop_words = {'the', 'is', 'at', 'which', 'on', 'a', 'an', 'and', 'or', 'but', 'in', 'with', 'to', 'for', 'of', 'as', 'by', 'what', 'where', 'how', 'when', 'why', 'who'}
        keywords = [word for word in words if word not in stop_words and len(word) > 2]

        return keywords

    def _search_coordinates(self, query: str, top_k: int) -> List[Dict[str, Any]]:
        """Enhanced coordinate search using intelligent label detection"""

        # First, try to identify the target label from the query
        target_label = self._identify_target_label(query.lower())

        if target_label:
            # Search specifically for the identified target label
            coordinate_results = self._search_by_target_label(target_label)
            if coordinate_results:
                print(f"🎯 Found exact match for target label: '{target_label}'")
                return coordinate_results

        # Fallback to general search if no specific target found
        return self._search_coordinates_general(query, top_k)

    def _search_by_target_label(self, target_label: str) -> List[Dict[str, Any]]:
        """Search for coordinates that match the specific target label"""
        coordinate_results = []

        # Get all items from the collection
        all_items = self.coordinates_collection.get()

        for i, doc in enumerate(all_items['documents']):
            metadata = all_items['metadatas'][i]
            label = metadata.get('label', '').lower()

            # Check for exact label match or partial match
            if (target_label == label or
                target_label in label or
                label in target_label or
                self._labels_are_similar(target_label, label)):

                coordinate_results.append({
                    "document": doc,
                    "metadata": metadata,
                    "distance": 0.1,  # High confidence for exact matches
                    "match_type": "target_label"
                })
                print(f"📍 Target label match: '{target_label}' → '{label}'")

        return coordinate_results

    def _labels_are_similar(self, label1: str, label2: str) -> bool:
        """Check if two labels are similar enough to be considered a match"""
        # Handle common variations
        variations = {
            'video': ['video embeds', 'embedded player', 'video with controls'],
            'heading': ['main heading', 'page heading', 'section heading', 'sub heading', 'card heading'],
            'button': ['primary button', 'secondary button', 'icon button', 'ghost button'],
            'image': ['standard image', 'interactive image', 'thumbnail image', 'image with caption'],
            'navigation': ['navigation bars', 'top navigation bar', 'sidebar navigation', 'bottom navigation'],
            'menu': ['dropdown menu', 'context menu', 'hamburger menu'],
            'icon': ['standard icon', 'interactive icon', 'status icon', 'social icon'],
            'form': ['basic form', 'login form', 'signup form', 'multi-step form'],
            'card': ['standard card', 'image card', 'testimonial card'],
            'list': ['ordered list', 'unordered list', 'bulleted list'],
            'tab': ['standard tab', 'icon tab', 'scrollable tab bar'],
            'slider': ['continuous slider', 'discrete slider', 'range slider'],
            'checkbox': ['basic checkbox', 'tri-state checkbox', 'disabled checkbox'],
            'toggle': ['basic toggle', 'labeled toggle', 'on/off toggle'],
            'dropdown': ['multi-select dropdown', 'searchable dropdown', 'disabled dropdown']
        }

        # Check if label1 is a base form of label2 or vice versa
        for base, variants in variations.items():
            if label1 == base and any(variant in label2 for variant in variants):
                return True
            if label2 == base and any(variant in label1 for variant in variants):
                return True

        return False

    def _search_coordinates_general(self, query: str, top_k: int) -> List[Dict[str, Any]]:
        """General coordinate search using vector search or text matching"""
        if self.embeddings is not None:
            # Use vector search if embeddings are available
            results = self.coordinates_collection.query(
                query_texts=[query],
                n_results=top_k
            )

            coordinate_results = []
            if results['documents'] and results['documents'][0]:
                for i, doc in enumerate(results['documents'][0]):
                    metadata = results['metadatas'][0][i]
                    coordinate_results.append({
                        "document": doc,
                        "metadata": metadata,
                        "distance": results['distances'][0][i] if 'distances' in results else None,
                        "match_type": "vector_search"
                    })
        else:
            # Fallback to simple text matching
            all_items = self.coordinates_collection.get()
            coordinate_results = []

            query_lower = query.lower()
            for i, doc in enumerate(all_items['documents']):
                metadata = all_items['metadatas'][i]
                # Simple keyword matching
                if any(keyword in doc.lower() for keyword in query_lower.split()):
                    coordinate_results.append({
                        "document": doc,
                        "metadata": metadata,
                        "distance": 0.5,  # Default similarity score
                        "match_type": "text_matching"
                    })

            # Limit results
            coordinate_results = coordinate_results[:top_k]

        return coordinate_results

    def _extract_indices_from_coordinates(self, coordinate_results: List[Dict[str, Any]]) -> List[int]:
        """Extract indices from coordinate search results"""
        indices = []
        for result in coordinate_results:
            metadata = result["metadata"]
            if "index" in metadata:
                indices.append(metadata["index"])
        return list(set(indices))  # Remove duplicates

    def _retrieve_dom_data_by_indices(self, indices: List[int]) -> List[Dict[str, Any]]:
        """Retrieve complete DOM data using the extracted indices with enhanced vector DB lookup"""
        dom_context = []

        for index in indices:
            element_key = f"element_{index + 1}"

            # First try to get from vector database for enhanced metadata
            try:
                dom_results = self.dom_collection.query(
                    where={"index": index},
                    n_results=1
                )

                if dom_results['metadatas'] and dom_results['metadatas'][0]:
                    metadata = dom_results['metadatas'][0][0]
                    # Parse the full element data from vector DB
                    full_element_data = json.loads(metadata.get("full_element_data", "{}"))

                    # Also get coordinate information for this index
                    coordinate_info = None
                    if index < len(self.coordinates_data):
                        coordinate_info = self.coordinates_data[index]

                    dom_context.append({
                        "index": index,
                        "element_key": element_key,
                        "element_info": full_element_data,
                        "coordinate_info": coordinate_info,
                        "vector_metadata": metadata  # Include vector DB metadata
                    })
                    continue
            except Exception as e:
                print(f"⚠️ Vector DB lookup failed for index {index}: {e}")

            # Fallback to direct lookup from stored data
            if element_key in self.element_info_data:
                # Get the complete DOM information
                element_info = self.element_info_data[element_key]

                # Also get coordinate information for this index
                coordinate_info = None
                if index < len(self.coordinates_data):
                    coordinate_info = self.coordinates_data[index]

                dom_context.append({
                    "index": index,
                    "element_key": element_key,
                    "element_info": element_info,
                    "coordinate_info": coordinate_info
                })

        return dom_context

    def _combine_coordinate_and_dom_context(self, coordinate_results: List[Dict[str, Any]], dom_context: List[Dict[str, Any]]) -> str:
        """Combine coordinate and DOM information into a comprehensive context with enhanced details"""
        context_parts = []

        # Create a mapping of indices to DOM data for easy lookup
        dom_by_index = {item["index"]: item for item in dom_context}

        for coord_result in coordinate_results:
            coord_metadata = coord_result["metadata"]
            index = coord_metadata.get("index", -1)

            if index in dom_by_index:
                dom_item = dom_by_index[index]
                element_info = dom_item["element_info"]

                # Extract key computed style properties for better context
                computed_style = element_info.get('computedStyle', {})
                key_styles = {}
                important_style_props = ['display', 'position', 'width', 'height', 'color', 'background-color',
                                       'font-size', 'font-weight', 'border-radius', 'opacity', 'z-index']
                for prop in important_style_props:
                    if prop in computed_style:
                        key_styles[prop] = computed_style[prop]

                context_part = f"""
Element {index + 1} ({coord_metadata['label']}):
POSITION & SIZE:
- Coordinates: ({coord_metadata['x']}, {coord_metadata['y']})
- Dimensions: {coord_metadata['width']}px × {coord_metadata['height']}px
- Area: {coord_metadata['width'] * coord_metadata['height']} square pixels

DOM STRUCTURE:
- HTML Tag: <{element_info.get('tag', 'unknown')}>
- Text Content: "{element_info.get('text', 'No text content')}"
- CSS Selector: {element_info.get('cssSelector', 'N/A')}
- XPath: {element_info.get('xpath', 'N/A')}

STYLING & ATTRIBUTES:
- CSS Classes: {', '.join(element_info.get('classes', [])) or 'None'}
- Key Attributes: {', '.join([f"{k}='{v}'" for k, v in element_info.get('attributes', {}).items() if v]) or 'None'}
- Important Styles: {', '.join([f"{k}: {v}" for k, v in key_styles.items()]) or 'Default styles'}

FUNCTIONALITY:
- Source URL: {element_info.get('src', 'N/A')}
- Link Target: {element_info.get('href', 'N/A')}
- Inline Styles: {element_info.get('inlineStyle', 'None')}
"""
                context_parts.append(context_part.strip())

        return "\n\n".join(context_parts)

class UIAnalyzer:
    """Enhanced UI analyzer using Gemini AI with comprehensive vector database workflow and system message support"""

    def __init__(self):
        self.config = Config()
        self.processor = UIElementProcessor()

        # Initialize system message processor and heuristic evaluator
        self.system_processor = SystemMessageProcessor()
        self.heuristic_evaluator = HeuristicEvaluator()

        # Configure Gemini
        genai.configure(api_key=self.config.GOOGLE_API_KEY)
        self.model = genai.GenerativeModel(self.config.MODEL_NAME)
        print("✅ UIAnalyzer initialized with enhanced vector database workflow and system message support!")

    def set_system_message(self, message: str) -> Dict[str, Any]:
        """
        Set a system message to guide the analysis workflow

        Args:
            message: System message defining the analysis context and workflow

        Returns:
            Dictionary containing the parsed system message context
        """
        result = self.system_processor.set_system_message(message)
        print(f"🎯 System message set: {result['workflow_type']} workflow activated")

        if result['workflow_type'] == 'heuristic_evaluation':
            print("🔍 Heuristic evaluation mode: Will evaluate elements against usability principles")

        return result

    def get_system_context(self) -> Dict[str, Any]:
        """Get current system message context"""
        return self.system_processor.get_current_context()

    def analyze_query(self, query: str, screenshot_path: str = None) -> str:
        """
        Enhanced query analysis with system message support following the complete workflow:
        1. System Message Context: Apply system message workflow if set
        2. Query Understanding: Detect keywords
        3. Vector Search in Coordinates DB: Search for labels
        4. Index Extraction: Identify indices
        5. DOM Data Retrieval: Get corresponding DOM data
        6. Element Filtering: Apply system message filtering if required
        7. Heuristic Evaluation: Perform evaluation if in heuristic mode
        8. Context Injection: Store DOM data and pass as context
        9. Coordinate Inclusion: Include position information
        10. Model Response: Generate context-aware response
        """
        if screenshot_path is None:
            screenshot_path = self.config.DEFAULT_SCREENSHOT_PATH

        print(f"\n🔍 Analyzing query: '{query}'")

        # Get system message context
        system_context = self.system_processor.get_current_context()
        if system_context["system_message"]:
            print(f"🎯 System context: {system_context['workflow_type']} mode")

        print("=" * 50)

        # Check if this is a heuristic evaluation workflow
        if self.system_processor.is_heuristic_evaluation_mode():
            return self._perform_heuristic_evaluation(query, screenshot_path)

        # Use the enhanced workflow for other analysis types
        analysis_result = self.processor.analyze_query_and_retrieve_context(query)

        # Generate response using Gemini with enhanced context
        response = self._generate_enhanced_response(query, analysis_result, screenshot_path, system_context)

        return response

    def _perform_heuristic_evaluation(self, query: str, screenshot_path: str) -> str:
        """
        Perform comprehensive heuristic evaluation of all UI elements

        Args:
            query: User query (typically requesting evaluation)
            screenshot_path: Path to screenshot for reference

        Returns:
            Formatted heuristic evaluation report
        """
        print("🔍 Starting heuristic evaluation...")

        # Get all elements for evaluation
        evaluation_results = []
        total_elements = len(self.processor.coordinates_data)
        evaluated_count = 0

        for i, coord_data in enumerate(self.processor.coordinates_data):
            # Check if element should be evaluated (filter out excluded labels)
            if not self.heuristic_evaluator.should_evaluate_element(coord_data):
                print(f"⏭️ Skipping element {i+1}: {coord_data.get('label')} (excluded)")
                continue

            # Get corresponding DOM data
            element_key = f"element_{i+1}"
            if element_key in self.processor.element_info_data:
                element_data = self.processor.element_info_data[element_key]

                # Perform heuristic evaluation
                evaluation = self.heuristic_evaluator.evaluate_element(element_data, coord_data)
                evaluation_results.append(evaluation)
                evaluated_count += 1

                print(f"✅ Evaluated element {i+1}/{total_elements}: {coord_data.get('label')}")

        print(f"🎯 Evaluation complete: {evaluated_count} elements evaluated")

        # Generate comprehensive report
        return self._generate_heuristic_report(evaluation_results, query, screenshot_path)

    def _generate_heuristic_report(self, evaluation_results: List[Dict[str, Any]], query: str, screenshot_path: str) -> str:
        """
        Generate a comprehensive heuristic evaluation report using Gemini analysis

        Args:
            evaluation_results: List of evaluation results for each element (from Gemini)
            query: Original user query
            screenshot_path: Path to screenshot

        Returns:
            Formatted heuristic evaluation report with Gemini insights
        """
        # Prepare summary statistics
        total_elements = len(evaluation_results)
        elements_with_violations = sum(1 for result in evaluation_results if result["violations"])
        total_violations = sum(len(result["violations"]) for result in evaluation_results)

        # Group violations by severity
        high_severity = []
        medium_severity = []
        low_severity = []

        for result in evaluation_results:
            for violation in result["violations"]:
                if violation.get("severity") == "high":
                    high_severity.append((result["element_info"], violation))
                elif violation.get("severity") == "medium":
                    medium_severity.append((result["element_info"], violation))
                else:
                    low_severity.append((result["element_info"], violation))

        # Create detailed report sections
        violations_report = self._format_violations_report(evaluation_results)

        # Generate overall Gemini analysis of all results
        overall_analysis = self._generate_overall_gemini_analysis(evaluation_results, query, screenshot_path)

        # Combine into final report
        report = f"""
# 🤖 GEMINI-POWERED HEURISTIC EVALUATION REPORT

## 📊 EVALUATION SUMMARY
- **Total Elements Evaluated**: {total_elements}
- **Elements with Violations**: {elements_with_violations}
- **Total Violations Found**: {total_violations}
- **High Severity Issues**: {len(high_severity)}
- **Medium Severity Issues**: {len(medium_severity)}
- **Low Severity Issues**: {len(low_severity)}

## 🚨 DETAILED ELEMENT EVALUATIONS

{violations_report}

## 🧠 OVERALL GEMINI ANALYSIS

{overall_analysis}

---
*Report generated by Gemini AI for query: "{query}"*
*Screenshot reference: {screenshot_path}*
*Each element was individually evaluated by Gemini against all 10 Nielsen heuristics*
"""

        return report.strip()

    def _format_violations_report(self, evaluation_results: List[Dict[str, Any]]) -> str:
        """Format violations into a readable report with Gemini analysis"""
        report_sections = []

        for result in evaluation_results:
            element_info = result["element_info"]
            violations = result["violations"]
            gemini_analysis = result.get("gemini_analysis", "")
            overall_score = result.get("overall_score", 0)
            recommendations = result.get("recommendations", [])

            # Element header
            section = f"""
### 🤖 Element {element_info['index'] + 1}: {element_info['label']}
**Location**: ({element_info['coordinates'].get('x', 0)}, {element_info['coordinates'].get('y', 0)})
**Size**: {element_info['coordinates'].get('width', 0)}×{element_info['coordinates'].get('height', 0)}px
**Tag**: `{element_info['tag']}`
**CSS Selector**: `{element_info['css_selector']}`
**Gemini Score**: {overall_score:.1f}%"""

            if violations:
                # Element has violations
                section += f"""

**🚨 Violations Found by Gemini:**"""

                for violation in violations:
                    severity_emoji = "🔴" if violation.get("severity") == "high" else "🟡" if violation.get("severity") == "medium" else "🟢"
                    section += f"""
- {severity_emoji} **{violation.get('heuristic', 'General Usability')}**
  - *Violation*: {violation.get('violation', 'Not specified')}
  - *Reason*: {violation.get('reason', 'Identified by Gemini analysis')}
  - *Severity*: {violation.get('severity', 'medium').title()}"""

                    # Add recommendation if available in violation
                    if 'recommendation' in violation:
                        section += f"""
  - *Recommendation*: {violation['recommendation']}"""

                # Add overall recommendations
                if recommendations:
                    section += f"""

**💡 Gemini Recommendations:**"""
                    for rec in recommendations:
                        section += f"""
- {rec}"""

            else:
                # No violations found
                section += f"""

✅ **No heuristic violations found by Gemini for this element.**"""

            # Add Gemini's overall analysis if available
            if gemini_analysis:
                section += f"""

**🧠 Gemini Analysis**: {gemini_analysis}"""

            report_sections.append(section)

        return "\n".join(report_sections) if report_sections else "No elements were evaluated."

    def _generate_overall_gemini_analysis(self, evaluation_results: List[Dict[str, Any]], query: str, screenshot_path: str) -> str:
        """Generate overall Gemini analysis of all evaluation results"""

        # Prepare comprehensive summary for Gemini
        summary_data = {
            "total_elements": len(evaluation_results),
            "elements_with_violations": sum(1 for result in evaluation_results if result["violations"]),
            "average_score": sum(result.get("overall_score", 0) for result in evaluation_results) / len(evaluation_results) if evaluation_results else 0,
            "element_summaries": []
        }

        for result in evaluation_results:
            element_summary = {
                "label": result["element_info"]["label"],
                "score": result.get("overall_score", 0),
                "violations_count": len(result["violations"]),
                "key_issues": [v.get("heuristic", "General") for v in result["violations"]],
                "gemini_analysis": result.get("gemini_analysis", "")
            }
            summary_data["element_summaries"].append(element_summary)

        # Create prompt for overall analysis
        prompt = f"""
You are a senior UX consultant providing an executive summary of a comprehensive heuristic evaluation.

EVALUATION OVERVIEW:
- Total UI elements evaluated: {summary_data['total_elements']}
- Elements with usability issues: {summary_data['elements_with_violations']}
- Average usability score: {summary_data['average_score']:.1f}%

DETAILED ELEMENT ANALYSIS:
{json.dumps(summary_data['element_summaries'], indent=2)}

USER QUERY: {query}
SCREENSHOT REFERENCE: {screenshot_path}

PROVIDE AN EXECUTIVE SUMMARY INCLUDING:

1. **OVERALL USABILITY ASSESSMENT**
   - Page-level usability rating and key findings
   - Most critical issues affecting user experience

2. **PRIORITY ACTION ITEMS**
   - Top 3 most important fixes ranked by impact
   - Quick wins vs. major improvements

3. **USABILITY PATTERNS**
   - Common issues across multiple elements
   - Systemic design problems

4. **STRATEGIC RECOMMENDATIONS**
   - Design system improvements
   - Process recommendations to prevent future issues

5. **COMPLIANCE ASSESSMENT**
   - How well the page follows Nielsen's heuristics
   - Areas of strength and weakness

Focus on actionable insights that will have the biggest impact on user experience.
"""

        try:
            response = self.model.generate_content(prompt)
            return response.text
        except Exception as e:
            return f"❌ Error generating overall Gemini analysis: {str(e)}"

    def _generate_enhanced_response(self, query: str, analysis_result: Dict[str, Any], screenshot_path: str, system_context: Dict[str, Any] = None) -> str:
        """Generate response using Gemini with enhanced context from the workflow"""
        if system_context is None:
            system_context = {}

        # Extract information from analysis result
        keywords = analysis_result["keywords"]
        combined_context = analysis_result["combined_context"]
        coordinate_matches = analysis_result["coordinate_matches"]
        dom_context = analysis_result["dom_context"]

        # Get system message instructions if available
        system_instructions = system_context.get("instructions", "")
        workflow_type = system_context.get("workflow_type", "default")

        # Create enhanced prompt with workflow information
        base_prompt = f"""
You are an expert UI/UX analyst with access to comprehensive webpage analysis data. You have analyzed a user's question using an advanced workflow that includes:

1. ✅ Query Understanding: Detected keywords: {', '.join(keywords)}
2. ✅ Vector Search: Found {len(coordinate_matches)} relevant UI elements
3. ✅ Index Extraction: Retrieved {len(dom_context)} complete DOM elements
4. ✅ Context Integration: Combined coordinate and DOM information
5. ✅ Position Awareness: Included exact pixel coordinates

SYSTEM CONTEXT:
- Analysis Mode: {workflow_type.replace('_', ' ').title()}
- System Instructions: {system_instructions}

User Question: {query}

Screenshot Reference: The user has provided a screenshot at {screenshot_path}

COMPREHENSIVE UI ELEMENT ANALYSIS:
{combined_context}

ANALYSIS WORKFLOW SUMMARY:
- Keywords detected: {', '.join(keywords)}
- Elements found: {len(coordinate_matches)} coordinate matches
- DOM data retrieved: {len(dom_context)} complete elements
- Position data: Exact pixel coordinates included
"""

        if workflow_type == "default":
            prompt = base_prompt + """
Please provide a comprehensive, position-aware response that:
1. Directly addresses the user's question using the detected keywords
2. References specific UI elements by their exact positions and properties
3. Explains the functionality, purpose, and technical details of the elements
4. Provides precise coordinate information (x, y, width, height)
5. Includes relevant DOM information (HTML tags, CSS selectors, XPath)
6. Uses clear, user-friendly language while being technically accurate
7. Mentions how the element relates to the overall page structure

Response:
"""
        else:
            prompt = base_prompt + f"""
Please provide a {workflow_type.replace('_', ' ')} response that:
1. Follows the system instructions provided above
2. Addresses the user's question in the context of {workflow_type.replace('_', ' ')}
3. References specific UI elements with their positions and properties
4. Provides actionable insights based on the analysis type
5. Uses the detected keywords and element context appropriately

Response:
"""

        try:
            response = self.model.generate_content(prompt)
            return response.text
        except Exception as e:
            return f"❌ Error generating response: {str(e)}"

    def initialize_data(self, coordinates_path: str = None, element_info_path: str = None):
        """Initialize the enhanced system with UI data"""
        if coordinates_path is None:
            coordinates_path = self.config.DEFAULT_COORDINATES_PATH
        if element_info_path is None:
            element_info_path = self.config.DEFAULT_ELEMENT_INFO_PATH

        print(f"\n📊 Initializing enhanced vector database system...")
        print(f"📍 Coordinates file: {coordinates_path}")
        print(f"🏗️ Element info file: {element_info_path}")

        self.processor.process_and_store_data(coordinates_path, element_info_path)
        print("✅ Enhanced UI data initialized successfully!")

    def get_analysis_summary(self, query: str) -> Dict[str, Any]:
        """Get detailed analysis summary without generating AI response"""
        return self.processor.analyze_query_and_retrieve_context(query)

    def perform_heuristic_evaluation(self, query: str = "Apply heuristic evaluation on this webpage.") -> str:
        """
        Convenience method to perform heuristic evaluation

        Args:
            query: Query for the evaluation (optional)

        Returns:
            Heuristic evaluation report
        """
        # Set heuristic evaluation system message
        self.set_system_message("You need to perform a heuristic evaluation of this web page. This is a landing page.")

        # Perform the evaluation
        return self.analyze_query(query)

def main():
    """Enhanced main function for testing the new workflow with system message support"""
    print("🚀 Starting Enhanced UI Element Analyzer with System Message Support")
    print("=" * 80)

    # Initialize the analyzer
    analyzer = UIAnalyzer()

    # Initialize with default data
    analyzer.initialize_data()

    # Test 1: Regular workflow
    print(f"\n🧪 Test 1: Regular Analysis Workflow")
    print("=" * 60)

    regular_queries = [
        "What is the video element on the page?",
        "Where is the main heading shown on the page?",
        "Tell me about the figure element and its location"
    ]

    for i, query in enumerate(regular_queries, 1):
        print(f"\n🔍 Regular Test {i}: {query}")
        print("-" * 50)

        try:
            response = analyzer.analyze_query(query)
            print(f"🤖 Response: {response[:200]}..." if len(response) > 200 else f"🤖 Response: {response}")
        except Exception as e:
            print(f"❌ Error: {str(e)}")

    # Test 2: Heuristic Evaluation Workflow
    print(f"\n\n🧪 Test 2: Heuristic Evaluation Workflow")
    print("=" * 60)

    # Set system message for heuristic evaluation
    system_message = "You need to perform a heuristic evaluation of this web page. This is a landing page."
    print(f"🎯 Setting system message: {system_message}")

    context = analyzer.set_system_message(system_message)
    print(f"✅ System context: {context['workflow_type']} mode activated")

    # Test heuristic evaluation
    heuristic_query = "Apply evaluation on this webpage."
    print(f"\n🔍 Heuristic Evaluation Query: {heuristic_query}")
    print("-" * 50)

    try:
        evaluation_report = analyzer.analyze_query(heuristic_query)
        print(f"📋 Evaluation Report:\n{evaluation_report}")
    except Exception as e:
        print(f"❌ Error in heuristic evaluation: {str(e)}")

    # Test 3: Convenience method
    print(f"\n\n🧪 Test 3: Convenience Method for Heuristic Evaluation")
    print("=" * 60)

    try:
        quick_evaluation = analyzer.perform_heuristic_evaluation()
        print(f"⚡ Quick Evaluation:\n{quick_evaluation}")
    except Exception as e:
        print(f"❌ Error in quick evaluation: {str(e)}")

    print("\n" + "=" * 80)
    print("✅ All tests completed!")

if __name__ == "__main__":
    main()
