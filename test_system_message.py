#!/usr/bin/env python3
"""
Test script for system message functionality

This script tests the new system message and heuristic evaluation features
without requiring API keys or external dependencies.
"""

import json
from system_message_processor import SystemMessageProcessor
from heuristic_evaluator import HeuristicEvaluator


def test_system_message_processor():
    """Test the SystemMessageProcessor functionality"""
    print("🧪 Testing SystemMessageProcessor")
    print("=" * 50)
    
    processor = SystemMessageProcessor()
    
    # Test 1: Heuristic evaluation message
    print("\n📋 Test 1: Heuristic Evaluation Message")
    message = "You need to perform a heuristic evaluation of this web page. This is a landing page."
    result = processor.set_system_message(message)
    
    print(f"✅ Workflow Type: {result['workflow_type']}")
    print(f"✅ Requires Filtering: {result['requires_filtering']}")
    print(f"✅ Instructions: {result['instructions'][:100]}...")
    
    assert result['workflow_type'] == 'heuristic_evaluation'
    assert result['requires_filtering'] == True
    
    # Test 2: Accessibility message
    print("\n📋 Test 2: Accessibility Assessment Message")
    message = "Perform accessibility assessment focusing on WCAG compliance."
    result = processor.set_system_message(message)
    
    print(f"✅ Workflow Type: {result['workflow_type']}")
    print(f"✅ Requires Filtering: {result['requires_filtering']}")
    
    assert result['workflow_type'] == 'accessibility_assessment'
    assert result['requires_filtering'] == True
    
    # Test 3: Design review message
    print("\n📋 Test 3: Design Review Message")
    message = "Conduct design review focusing on visual hierarchy and typography."
    result = processor.set_system_message(message)
    
    print(f"✅ Workflow Type: {result['workflow_type']}")
    print(f"✅ Requires Filtering: {result['requires_filtering']}")
    
    assert result['workflow_type'] == 'design_review'
    assert result['requires_filtering'] == False
    
    # Test 4: Default message
    print("\n📋 Test 4: Default/Empty Message")
    result = processor.set_system_message("")
    
    print(f"✅ Workflow Type: {result['workflow_type']}")
    print(f"✅ Requires Filtering: {result['requires_filtering']}")
    
    assert result['workflow_type'] == 'default'
    assert result['requires_filtering'] == False
    
    print("\n✅ All SystemMessageProcessor tests passed!")


def test_heuristic_evaluator():
    """Test the HeuristicEvaluator functionality"""
    print("\n\n🧪 Testing HeuristicEvaluator")
    print("=" * 50)
    
    evaluator = HeuristicEvaluator()
    
    # Test 1: Element filtering
    print("\n📋 Test 1: Element Filtering")
    
    # Should evaluate
    coord_data_1 = {"label": "Video", "coordinates": {"x": 100, "y": 100, "width": 200, "height": 150}}
    should_eval_1 = evaluator.should_evaluate_element(coord_data_1)
    print(f"✅ Video element should be evaluated: {should_eval_1}")
    assert should_eval_1 == True
    
    # Should not evaluate
    coord_data_2 = {"label": "Non-UI Element", "coordinates": {"x": 100, "y": 100, "width": 200, "height": 150}}
    should_eval_2 = evaluator.should_evaluate_element(coord_data_2)
    print(f"✅ Non-UI Element should not be evaluated: {should_eval_2}")
    assert should_eval_2 == False
    
    # Test 2: Button evaluation (with violations)
    print("\n📋 Test 2: Button Evaluation (with violations)")
    
    element_data = {
        "tag": "button",
        "text": "",  # No text - violation
        "cssSelector": "button.small",
        "xpath": "//button[1]",
        "computedStyle": {"cursor": "default"}  # No pointer cursor - violation
    }
    
    coord_data = {
        "label": "Primary Button",
        "coordinates": {"x": 100, "y": 100, "width": 30, "height": 20},  # Too small - violation
        "index": 0
    }
    
    evaluation = evaluator.evaluate_element(element_data, coord_data)
    
    print(f"✅ Element: {evaluation['element_info']['label']}")
    print(f"✅ Violations found: {len(evaluation['violations'])}")
    print(f"✅ Overall score: {evaluation['overall_score']:.1f}%")
    print(f"✅ Recommendations: {len(evaluation['recommendations'])}")
    
    # Should have violations
    assert len(evaluation['violations']) > 0
    assert evaluation['overall_score'] < 100
    assert len(evaluation['recommendations']) > 0
    
    # Print violations for inspection
    for violation in evaluation['violations']:
        print(f"   🔴 {violation['heuristic']}: {violation['violation']} ({violation['severity']})")
    
    # Test 3: Good element evaluation (no violations)
    print("\n📋 Test 3: Good Element Evaluation (no violations)")
    
    good_element_data = {
        "tag": "h1",
        "text": "Welcome to Our Website",
        "cssSelector": "h1.main-heading",
        "xpath": "//h1[1]",
        "computedStyle": {"cursor": "default"}
    }
    
    good_coord_data = {
        "label": "Main Heading",
        "coordinates": {"x": 100, "y": 100, "width": 400, "height": 60},
        "index": 1
    }
    
    good_evaluation = evaluator.evaluate_element(good_element_data, good_coord_data)
    
    print(f"✅ Element: {good_evaluation['element_info']['label']}")
    print(f"✅ Violations found: {len(good_evaluation['violations'])}")
    print(f"✅ Overall score: {good_evaluation['overall_score']:.1f}%")
    
    # Should have no violations for heading
    assert len(good_evaluation['violations']) == 0
    assert good_evaluation['overall_score'] == 100
    
    print("\n✅ All HeuristicEvaluator tests passed!")


def test_integration():
    """Test integration between components"""
    print("\n\n🧪 Testing Integration")
    print("=" * 50)
    
    processor = SystemMessageProcessor()
    evaluator = HeuristicEvaluator()
    
    # Set heuristic evaluation mode
    message = "You need to perform a heuristic evaluation of this web page. This is a landing page."
    result = processor.set_system_message(message)
    
    print(f"✅ System message set: {result['workflow_type']}")
    print(f"✅ Heuristic mode active: {processor.is_heuristic_evaluation_mode()}")
    
    # Test element filtering in heuristic mode
    test_elements = [
        {"label": "Video", "coordinates": {"x": 100, "y": 100, "width": 200, "height": 150}},
        {"label": "Non-UI Element", "coordinates": {"x": 100, "y": 100, "width": 200, "height": 150}},
        {"label": "Primary Button", "coordinates": {"x": 100, "y": 100, "width": 100, "height": 40}},
        {"label": "Unknown Element", "coordinates": {"x": 100, "y": 100, "width": 200, "height": 150}}
    ]
    
    evaluable_count = 0
    for element in test_elements:
        if evaluator.should_evaluate_element(element):
            evaluable_count += 1
            print(f"✅ Will evaluate: {element['label']}")
        else:
            print(f"⏭️ Will skip: {element['label']}")
    
    print(f"\n✅ Total elements to evaluate: {evaluable_count} out of {len(test_elements)}")
    assert evaluable_count == 2  # Should evaluate Video and Primary Button only
    
    print("\n✅ All integration tests passed!")


def main():
    """Run all tests"""
    print("🚀 Starting System Message Functionality Tests")
    print("=" * 80)
    
    try:
        test_system_message_processor()
        test_heuristic_evaluator()
        test_integration()
        
        print("\n" + "=" * 80)
        print("🎉 ALL TESTS PASSED! System message functionality is working correctly.")
        print("\n📋 Summary:")
        print("✅ SystemMessageProcessor: Correctly identifies workflow types")
        print("✅ HeuristicEvaluator: Properly evaluates elements and detects violations")
        print("✅ Element Filtering: Correctly excludes Non-UI and Unknown elements")
        print("✅ Integration: Components work together seamlessly")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        raise


if __name__ == "__main__":
    main()
