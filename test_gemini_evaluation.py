#!/usr/bin/env python3
"""
Test script for Gemini-powered heuristic evaluation

This script tests the new pure Gemini-based evaluation system where Gemini AI
evaluates each UI element individually against all 10 Nielsen heuristics.
"""

import sys
import os
import json
from typing import Dict, Any

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ui_analyzer import UIAnalyzer
from heuristic_evaluator import HeuristicEvaluator


def test_gemini_evaluator():
    """Test the new Gemini-powered heuristic evaluator"""
    print("🤖 Testing Gemini-Powered Heuristic Evaluator")
    print("=" * 60)
    
    try:
        # Initialize evaluator
        evaluator = HeuristicEvaluator()
        print("✅ Gemini evaluator initialized successfully")
        
        # Test with sample element data
        sample_element_data = {
            "tag": "button",
            "text": "",  # Empty text - should trigger violation
            "cssSelector": "button.cta",
            "xpath": "//button[1]",
            "computedStyle": {
                "cursor": "default",  # Should trigger violation
                "width": "30px",
                "height": "20px"
            },
            "attributes": {
                "class": "cta",
                "type": "button"
            }
        }
        
        sample_coordinate_data = {
            "index": 0,
            "label": "Primary Button",
            "coordinates": {
                "x": 100,
                "y": 200,
                "width": 30,  # Too small - should trigger violation
                "height": 20   # Too small - should trigger violation
            }
        }
        
        print(f"\n🔍 Testing element evaluation:")
        print(f"Element: {sample_coordinate_data['label']}")
        print(f"Size: {sample_coordinate_data['coordinates']['width']}x{sample_coordinate_data['coordinates']['height']}px")
        print(f"Text: '{sample_element_data['text']}'")
        
        # Perform Gemini evaluation
        print("\n🤖 Calling Gemini for evaluation...")
        result = evaluator.evaluate_element(sample_element_data, sample_coordinate_data)
        
        # Display results
        print(f"\n📊 Evaluation Results:")
        print(f"Status: {result.get('evaluation_status', 'unknown')}")
        print(f"Overall Score: {result.get('overall_score', 0):.1f}%")
        print(f"Violations Found: {len(result.get('violations', []))}")
        print(f"Passed Checks: {len(result.get('passed_checks', []))}")
        
        # Show violations
        violations = result.get('violations', [])
        if violations:
            print(f"\n🚨 Violations Found by Gemini:")
            for i, violation in enumerate(violations, 1):
                print(f"{i}. {violation.get('heuristic', 'Unknown')}")
                print(f"   Violation: {violation.get('violation', 'Not specified')}")
                print(f"   Severity: {violation.get('severity', 'unknown')}")
                print(f"   Reason: {violation.get('reason', 'Not provided')}")
                if 'recommendation' in violation:
                    print(f"   Recommendation: {violation['recommendation']}")
                print()
        
        # Show Gemini analysis
        gemini_analysis = result.get('gemini_analysis', '')
        if gemini_analysis:
            print(f"🧠 Gemini Analysis:")
            print(gemini_analysis)
        
        # Show recommendations
        recommendations = result.get('recommendations', [])
        if recommendations:
            print(f"\n💡 Gemini Recommendations:")
            for i, rec in enumerate(recommendations, 1):
                print(f"{i}. {rec}")
        
        print("\n✅ Gemini evaluation test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error in Gemini evaluation test: {str(e)}")
        return False


def test_full_system_integration():
    """Test the full system with Gemini-powered evaluation"""
    print("\n🔗 Testing Full System Integration with Gemini")
    print("=" * 60)
    
    try:
        # Initialize analyzer
        analyzer = UIAnalyzer()
        analyzer.initialize_data()
        print("✅ UI Analyzer initialized with default data")
        
        # Set heuristic evaluation mode
        system_message = "You need to perform a heuristic evaluation of this web page. This is a landing page."
        context = analyzer.set_system_message(system_message)
        print(f"✅ System context: {context['workflow_type']} mode activated")
        
        # Perform heuristic evaluation
        print("\n🤖 Performing Gemini-powered heuristic evaluation...")
        evaluation_query = "Apply comprehensive heuristic evaluation on this webpage using Gemini AI."
        
        report = analyzer.analyze_query(evaluation_query)
        
        # Display report summary
        print(f"\n📋 Evaluation Report Generated:")
        print(f"Report length: {len(report)} characters")
        
        # Show first part of report
        if len(report) > 500:
            print(f"\n📄 Report Preview (first 500 chars):")
            print(report[:500] + "...")
        else:
            print(f"\n📄 Full Report:")
            print(report)
        
        # Check for Gemini-specific content
        gemini_indicators = [
            "GEMINI-POWERED",
            "Gemini Score",
            "Violations Found by Gemini",
            "Gemini Analysis",
            "Gemini Recommendations"
        ]
        
        found_indicators = [indicator for indicator in gemini_indicators if indicator in report]
        print(f"\n🔍 Gemini Integration Check:")
        print(f"Found {len(found_indicators)}/{len(gemini_indicators)} Gemini indicators in report")
        
        for indicator in found_indicators:
            print(f"✅ {indicator}")
        
        print("\n✅ Full system integration test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error in full system test: {str(e)}")
        return False


def test_element_filtering():
    """Test that element filtering still works with Gemini evaluation"""
    print("\n🔍 Testing Element Filtering with Gemini")
    print("=" * 60)
    
    try:
        evaluator = HeuristicEvaluator()
        
        # Test elements that should be evaluated
        evaluable_elements = [
            {"label": "Primary Button"},
            {"label": "Video"},
            {"label": "Text Input"},
            {"label": "Navigation Link"}
        ]
        
        # Test elements that should be excluded
        excluded_elements = [
            {"label": "Non-UI Element"},
            {"label": "Unknown Element"}
        ]
        
        print("🔍 Testing evaluable elements:")
        for element in evaluable_elements:
            should_eval = evaluator.should_evaluate_element(element)
            print(f"✅ {element['label']}: {should_eval}")
            assert should_eval == True, f"Should evaluate {element['label']}"
        
        print("\n🔍 Testing excluded elements:")
        for element in excluded_elements:
            should_eval = evaluator.should_evaluate_element(element)
            print(f"⏭️ {element['label']}: {should_eval}")
            assert should_eval == False, f"Should exclude {element['label']}"
        
        print("\n✅ Element filtering test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Error in element filtering test: {str(e)}")
        return False


def main():
    """Run all Gemini evaluation tests"""
    print("🚀 Starting Gemini-Powered Evaluation Tests")
    print("=" * 80)
    
    tests = [
        test_gemini_evaluator,
        test_element_filtering,
        test_full_system_integration
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {str(e)}")
            failed += 1
    
    print("\n" + "=" * 80)
    print(f"🎉 GEMINI EVALUATION TEST RESULTS:")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📊 Success Rate: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("\n🎉 ALL TESTS PASSED! Gemini-powered evaluation system is working!")
        print("\n🤖 Key Features Verified:")
        print("✅ Gemini evaluates each element individually")
        print("✅ All 10 Nielsen heuristics are checked by Gemini")
        print("✅ Structured JSON responses from Gemini")
        print("✅ Element filtering still works")
        print("✅ Full system integration successful")
        print("✅ Comprehensive reports with Gemini insights")
    else:
        print(f"\n⚠️ {failed} test(s) failed. Please review the errors above.")
    
    return failed == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
