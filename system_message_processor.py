"""
System Message Processor

This module handles system message parsing, context management, and workflow determination
for the UI Element Analyzer system.
"""

from typing import Dict, List, Any, Optional, Tuple
import re
import json
from config import Config


class SystemMessageProcessor:
    """
    Processes system messages to determine analysis workflows and context.
    
    Supports various analysis types including:
    - Heuristic evaluation
    - Accessibility assessment
    - Design review
    - Usability testing
    - Custom analysis workflows
    """
    
    def __init__(self):
        self.config = Config()
        self.current_system_message = ""
        self.analysis_context = {}
        self.workflow_type = "default"
        
        # Define supported analysis workflows
        self.supported_workflows = {
            "heuristic_evaluation": {
                "keywords": ["heuristic", "evaluation", "usability", "nielsen", "principles"],
                "description": "Comprehensive heuristic evaluation of UI elements",
                "requires_element_filtering": True,
                "output_format": "structured_violations"
            },
            "accessibility_assessment": {
                "keywords": ["accessibility", "a11y", "wcag", "screen reader", "contrast"],
                "description": "Accessibility compliance assessment",
                "requires_element_filtering": True,
                "output_format": "compliance_report"
            },
            "design_review": {
                "keywords": ["design", "visual", "aesthetic", "layout", "typography"],
                "description": "Visual design and layout review",
                "requires_element_filtering": False,
                "output_format": "design_feedback"
            },
            "usability_testing": {
                "keywords": ["usability", "user experience", "ux", "testing", "interaction"],
                "description": "Usability testing and user experience evaluation",
                "requires_element_filtering": True,
                "output_format": "usability_report"
            },
            "performance_analysis": {
                "keywords": ["performance", "speed", "loading", "optimization"],
                "description": "Performance and optimization analysis",
                "requires_element_filtering": False,
                "output_format": "performance_metrics"
            }
        }
    
    def set_system_message(self, message: str) -> Dict[str, Any]:
        """
        Set and parse a new system message
        
        Args:
            message: The system message to process
            
        Returns:
            Dictionary containing parsed context and workflow information
        """
        self.current_system_message = message.strip()
        
        if not self.current_system_message:
            return self._reset_to_default()
        
        # Parse the system message
        parsing_result = self._parse_system_message(self.current_system_message)
        
        # Update internal state
        self.analysis_context = parsing_result["context"]
        self.workflow_type = parsing_result["workflow_type"]
        
        return parsing_result
    
    def _parse_system_message(self, message: str) -> Dict[str, Any]:
        """
        Parse system message to extract context and determine workflow
        
        Args:
            message: System message to parse
            
        Returns:
            Dictionary with parsing results
        """
        message_lower = message.lower()
        
        # Determine workflow type
        workflow_type = self._determine_workflow_type(message_lower)
        
        # Extract context information
        context = self._extract_context_information(message, workflow_type)
        
        # Generate instructions for the AI model
        instructions = self._generate_ai_instructions(workflow_type, context)
        
        return {
            "workflow_type": workflow_type,
            "context": context,
            "instructions": instructions,
            "requires_filtering": self.supported_workflows.get(workflow_type, {}).get("requires_element_filtering", False),
            "output_format": self.supported_workflows.get(workflow_type, {}).get("output_format", "default"),
            "original_message": message
        }
    
    def _determine_workflow_type(self, message_lower: str) -> str:
        """
        Determine the analysis workflow type based on message content
        
        Args:
            message_lower: Lowercase system message
            
        Returns:
            Workflow type identifier
        """
        # Check for explicit workflow keywords
        for workflow_type, workflow_info in self.supported_workflows.items():
            keywords = workflow_info["keywords"]
            if any(keyword in message_lower for keyword in keywords):
                return workflow_type
        
        # Default workflow
        return "default"
    
    def _extract_context_information(self, message: str, workflow_type: str) -> Dict[str, Any]:
        """
        Extract specific context information from the system message
        
        Args:
            message: Original system message
            workflow_type: Determined workflow type
            
        Returns:
            Dictionary containing extracted context
        """
        context = {
            "page_type": self._extract_page_type(message),
            "focus_areas": self._extract_focus_areas(message),
            "evaluation_criteria": self._extract_evaluation_criteria(message, workflow_type),
            "target_audience": self._extract_target_audience(message),
            "specific_requirements": self._extract_specific_requirements(message)
        }
        
        return context
    
    def _extract_page_type(self, message: str) -> Optional[str]:
        """Extract page type from system message"""
        page_patterns = [
            r"landing page",
            r"homepage",
            r"product page",
            r"checkout page",
            r"login page",
            r"dashboard",
            r"form page",
            r"search results",
            r"profile page",
            r"settings page"
        ]
        
        message_lower = message.lower()
        for pattern in page_patterns:
            if re.search(pattern, message_lower):
                return pattern.replace(" page", "").replace(" ", "_")
        
        return None
    
    def _extract_focus_areas(self, message: str) -> List[str]:
        """Extract specific focus areas from system message"""
        focus_patterns = [
            r"navigation",
            r"forms?",
            r"buttons?",
            r"images?",
            r"videos?",
            r"headers?",
            r"footers?",
            r"menus?",
            r"search",
            r"content",
            r"layout",
            r"typography",
            r"colors?",
            r"interactions?"
        ]
        
        focus_areas = []
        message_lower = message.lower()
        
        for pattern in focus_patterns:
            if re.search(pattern, message_lower):
                focus_areas.append(pattern.rstrip("s?"))
        
        return focus_areas
    
    def _extract_evaluation_criteria(self, message: str, workflow_type: str) -> List[str]:
        """Extract specific evaluation criteria based on workflow type"""
        criteria = []
        
        if workflow_type == "heuristic_evaluation":
            heuristic_patterns = [
                r"visibility",
                r"feedback",
                r"consistency",
                r"error prevention",
                r"recognition",
                r"flexibility",
                r"aesthetic",
                r"minimalist",
                r"error recovery",
                r"help",
                r"documentation"
            ]
            
            message_lower = message.lower()
            for pattern in heuristic_patterns:
                if re.search(pattern, message_lower):
                    criteria.append(pattern)
        
        elif workflow_type == "accessibility_assessment":
            a11y_patterns = [
                r"contrast",
                r"keyboard navigation",
                r"screen reader",
                r"alt text",
                r"focus",
                r"aria",
                r"semantic markup"
            ]
            
            message_lower = message.lower()
            for pattern in a11y_patterns:
                if re.search(pattern, message_lower):
                    criteria.append(pattern)
        
        return criteria
    
    def _extract_target_audience(self, message: str) -> Optional[str]:
        """Extract target audience information"""
        audience_patterns = [
            r"beginners?",
            r"experts?",
            r"elderly",
            r"children",
            r"professionals?",
            r"general users?",
            r"mobile users?",
            r"desktop users?"
        ]
        
        message_lower = message.lower()
        for pattern in audience_patterns:
            match = re.search(pattern, message_lower)
            if match:
                return match.group(0)
        
        return None
    
    def _extract_specific_requirements(self, message: str) -> List[str]:
        """Extract any specific requirements or constraints"""
        requirements = []
        
        # Look for specific requirements patterns
        requirement_patterns = [
            r"must be [\w\s]+",
            r"should [\w\s]+",
            r"needs to [\w\s]+",
            r"required to [\w\s]+",
            r"ensure [\w\s]+"
        ]
        
        for pattern in requirement_patterns:
            matches = re.findall(pattern, message, re.IGNORECASE)
            requirements.extend(matches)
        
        return requirements
    
    def _generate_ai_instructions(self, workflow_type: str, context: Dict[str, Any]) -> str:
        """
        Generate specific instructions for the AI model based on workflow and context
        
        Args:
            workflow_type: Type of analysis workflow
            context: Extracted context information
            
        Returns:
            Formatted instructions for the AI model
        """
        base_instruction = f"You are performing a {workflow_type.replace('_', ' ')} analysis."
        
        if workflow_type == "heuristic_evaluation":
            instructions = f"""
{base_instruction}

HEURISTIC EVALUATION INSTRUCTIONS:
1. Evaluate each UI element against established usability heuristics
2. Focus on elements that are NOT labeled as "Non-UI Element" or "Unknown Element"
3. For each element, check for violations of usability principles
4. Report violations with:
   - The specific heuristic violated
   - Clear reason for the violation
   - Element label and coordinates
   - Relevant DOM data
   - Severity level (high/medium/low)

5. If no violations are found for an element, explicitly state: "No heuristic violations found for this element."

CONTEXT:
- Page type: {context.get('page_type', 'Not specified')}
- Focus areas: {', '.join(context.get('focus_areas', [])) or 'All elements'}
- Target audience: {context.get('target_audience', 'General users')}
"""
        
        elif workflow_type == "accessibility_assessment":
            instructions = f"""
{base_instruction}

ACCESSIBILITY ASSESSMENT INSTRUCTIONS:
1. Evaluate elements for WCAG compliance and accessibility best practices
2. Check for proper semantic markup, keyboard navigation, and screen reader compatibility
3. Assess color contrast, focus indicators, and alternative text
4. Report accessibility issues with specific recommendations

CONTEXT:
- Page type: {context.get('page_type', 'Not specified')}
- Focus areas: {', '.join(context.get('focus_areas', [])) or 'All elements'}
"""
        
        else:
            instructions = f"""
{base_instruction}

GENERAL ANALYSIS INSTRUCTIONS:
1. Analyze UI elements based on the system message context
2. Provide detailed insights about element functionality and design
3. Include coordinate and DOM information for spatial awareness

CONTEXT:
- Page type: {context.get('page_type', 'Not specified')}
- Focus areas: {', '.join(context.get('focus_areas', [])) or 'All elements'}
"""
        
        return instructions.strip()
    
    def _reset_to_default(self) -> Dict[str, Any]:
        """Reset to default analysis mode"""
        self.current_system_message = ""
        self.analysis_context = {}
        self.workflow_type = "default"
        
        return {
            "workflow_type": "default",
            "context": {},
            "instructions": "Provide general analysis of UI elements based on user queries.",
            "requires_filtering": False,
            "output_format": "default",
            "original_message": ""
        }
    
    def get_current_context(self) -> Dict[str, Any]:
        """Get current system message context"""
        return {
            "system_message": self.current_system_message,
            "workflow_type": self.workflow_type,
            "context": self.analysis_context,
            "requires_filtering": self.supported_workflows.get(self.workflow_type, {}).get("requires_element_filtering", False)
        }
    
    def is_heuristic_evaluation_mode(self) -> bool:
        """Check if currently in heuristic evaluation mode"""
        return self.workflow_type == "heuristic_evaluation"
