{"cells": [{"cell_type": "markdown", "metadata": {"id": "header"}, "source": ["# 🔍 UI Element Analyzer with Enhanced Vector Database & System Messages\n", "\n", "## Advanced UI Analysis using LangChain, LangGraph, Vector Databases, and Gemini AI\n", "\n", "This notebook implements a sophisticated workflow for analyzing UI elements with **system message support** for guided analysis:\n", "\n", "### 🔄 **Enhanced Workflow:**\n", "1. **System Message Processing**: Parse system messages to determine analysis workflow\n", "2. **Query Understanding**: Detect keywords in user queries\n", "3. **Vector Search in Coordinates DB**: Search for relevant UI element labels\n", "4. **Index Extraction**: Identify indices where labels are found\n", "5. **Element Filtering**: Apply system message filtering (exclude Non-UI/Unknown elements)\n", "6. **DOM Data Retrieval**: Use indices to retrieve complete DOM data from element_info.json\n", "7. **Heuristic Evaluation**: Perform element-by-element evaluation if in heuristic mode\n", "8. **Context Injection**: Store DOM data in vector DB and pass as context\n", "9. **Coordinate Inclusion**: Include position information for spatial awareness\n", "10. **Model Response**: Generate context-aware responses using Gemini AI\n", "\n", "### 📊 **Key Features:**\n", "- ✅ **System Message Support**: Define analysis workflows (heuristic evaluation, accessibility, etc.)\n", "- ✅ **Heuristic Evaluation**: Comprehensive usability evaluation against established principles\n", "- ✅ **Element Filtering**: Exclude non-UI elements from evaluation\n", "- ✅ **Structured Reporting**: Detailed violation reports with severity levels\n", "- ✅ Complete element_info.json storage in vector database indexed by position\n", "- ✅ Semantic search for UI element discovery\n", "- ✅ Position-aware AI responses with exact coordinates\n", "- ✅ Comprehensive DOM data integration\n", "- ✅ Interactive query interface\n", "\n", "### 🎯 **System Message Examples:**\n", "- `\"You need to perform a heuristic evaluation of this web page. This is a landing page.\"`\n", "- `\"Perform accessibility assessment focusing on WCAG compliance.\"`\n", "- `\"Conduct design review focusing on visual hierarchy and typography.\"`\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {"id": "setup"}, "source": ["## 🚀 Setup and Installation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install_packages"}, "outputs": [], "source": ["# Install required packages\n", "!pip install -q langchain>=0.1.0\n", "!pip install -q langgraph>=0.0.26\n", "!pip install -q langchain-google-genai>=0.0.6\n", "!pip install -q chromadb>=0.4.22\n", "!pip install -q pillow>=10.2.0\n", "!pip install -q numpy>=1.26.3\n", "!pip install -q sentence-transformers>=2.2.0\n", "!pip install -q google-generativeai\n", "\n", "print(\"✅ All packages installed successfully!\")"]}, {"cell_type": "markdown", "metadata": {"id": "api_keys"}, "source": ["## 🔑 API Keys Configuration"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "setup_keys"}, "outputs": [], "source": ["import os\n", "from google.colab import userdata\n", "import getpass\n", "\n", "# Try to get API keys from Colab secrets first\n", "try:\n", "    GOOGLE_API_KEY = userdata.get('GOOGLE_API_KEY')\n", "    HUGGINGFACE_TOKEN = userdata.get('HUGGINGFACE_TOKEN')\n", "    print(\"✅ API keys loaded from Colab secrets\")\nexcept:\n", "    print(\"⚠️ API keys not found in secrets. Please enter them manually:\")\n", "    GOOGLE_API_KEY = getpass.getpass(\"Enter your Google API Key: \")\n", "    HUGGINGFACE_TOKEN = \"*************************************\"  # Default token\n", "\n", "# Set environment variables\n", "os.environ[\"GOOGLE_API_KEY\"] = GOOGLE_API_KEY\n", "os.environ[\"HUGGINGFACE_HUB_TOKEN\"] = HUGGINGFACE_TOKEN\n", "\n", "print(\"🔑 API keys configured successfully!\")"]}, {"cell_type": "markdown", "metadata": {"id": "config"}, "source": ["## ⚙️ Configuration Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "config_class"}, "outputs": [], "source": ["# Configuration class\n", "class Config:\n", "    # API Keys\n", "    GOOGLE_API_KEY = os.getenv(\"GOOGLE_API_KEY\")\n", "    HUGGINGFACE_TOKEN = os.getenv(\"HUGGINGFACE_HUB_TOKEN\", \"*************************************\")\n", "    \n", "    # ChromaDB configuration\n", "    CHROMA_DB_PATH = \"./chroma_db\"\n", "    COLLECTION_NAME = \"ui_elements\"\n", "    \n", "    # Model configuration\n", "    MODEL_NAME = \"gemini-2.5-flash-preview-05-20\"\n", "    TEMPERATURE = 0.7\n", "    MAX_TOKENS = 10000\n", "    \n", "    # Vector database configuration\n", "    EMBEDDING_MODEL = \"all-MiniLM-L6-v2\"\n", "    TOP_K_RESULTS = 3\n", "    \n", "    # Default file paths\n", "    DEFAULT_SCREENSHOT_PATH = \"notioncom.png\"\n", "    DEFAULT_COORDINATES_PATH = \"coordinates.json\"\n", "    DEFAULT_ELEMENT_INFO_PATH = \"element_info.json\"\n", "    DEFAULT_LABELS_PATH = \"label.txt\"\n", "\n", "config = Config()\n", "print(\"✅ Configuration loaded successfully!\")"]}, {"cell_type": "markdown", "metadata": {"id": "imports"}, "source": ["## 📦 Import Required Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "import_libraries"}, "outputs": [], "source": ["import json\n", "import re\n", "import chromadb\n", "import google.generativeai as genai\n", "from sentence_transformers import SentenceTransformer\n", "from typing import Dict, List, Any, Set\n", "from PIL import Image\n", "import numpy as np\n", "from difflib import SequenceMatcher\n", "import ipywidgets as widgets\n", "from IPython.display import display, HTML, clear_output\n", "import base64\n", "from io import BytesIO\n", "\n", "print(\"✅ All libraries imported successfully!\")"]}, {"cell_type": "markdown", "metadata": {"id": "core_classes"}, "source": ["## 🏗️ Core Classes Implementation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ui_element_processor"}, "outputs": [], "source": ["class UIElementProcessor:\n", "    \"\"\"Enhanced processor for UI elements with complete vector database workflow\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.config = config\n", "        \n", "        # Set Hugging Face token for model downloads\n", "        os.environ[\"HUGGINGFACE_HUB_TOKEN\"] = self.config.HUGGINGFACE_TOKEN\n", "        \n", "        try:\n", "            self.embeddings = SentenceTransformer(\n", "                self.config.EMBEDDING_MODEL,\n", "                token=self.config.HUGGINGFACE_TOKEN\n", "            )\n", "            print(\"✅ SentenceTransformer loaded successfully!\")\n", "        except Exception as e:\n", "            print(f\"❌ Could not load SentenceTransformer: {e}\")\n", "            self.embeddings = None\n", "            \n", "        self.chroma_client = chromadb.PersistentClient(path=self.config.CHROMA_DB_PATH)\n", "        self.coordinates_collection = None\n", "        self.dom_collection = None\n", "        self._initialize_collections()\n", "\n", "        # Store raw data for index-based retrieval\n", "        self.coordinates_data = []\n", "        self.element_info_data = {}\n", "        \n", "        # Load controlled vocabulary from label.txt\n", "        self.controlled_vocabulary = self._load_controlled_vocabulary()\n", "\n", "    def _initialize_collections(self):\n", "        \"\"\"Initialize or get existing ChromaDB collections for coordinates and DOM data\"\"\"\n", "        # Initialize coordinates collection\n", "        try:\n", "            self.coordinates_collection = self.chroma_client.get_collection(name=\"ui_coordinates\")\n", "            print(\"✅ Loaded existing coordinates collection\")\n", "        except:\n", "            self.coordinates_collection = self.chroma_client.create_collection(\n", "                name=\"ui_coordinates\",\n", "                metadata={\"description\": \"UI element coordinates and labels for semantic search\"}\n", "            )\n", "            print(\"✅ Created new coordinates collection\")\n", "\n", "        # Initialize DOM collection\n", "        try:\n", "            self.dom_collection = self.chroma_client.get_collection(name=\"ui_dom_data\")\n", "            print(\"✅ Loaded existing DOM collection\")\n", "        except:\n", "            self.dom_collection = self.chroma_client.create_collection(\n", "                name=\"ui_dom_data\",\n", "                metadata={\"description\": \"Complete DOM element information indexed by position\"}\n", "            )\n", "            print(\"✅ Created new DOM collection\")\n", "\n", "print(\"✅ UIElementProcessor class defined!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "controlled_vocabulary_loading"}, "outputs": [], "source": ["# Add controlled vocabulary loading method\n", "def _load_controlled_vocabulary(self):\n", "    \"\"\"Load controlled vocabulary from label.txt file for accurate keyword extraction\"\"\"\n", "    vocabulary = set()\n", "    \n", "    try:\n", "        # Try to load from configuration file path\n", "        with open(self.config.DEFAULT_LABELS_PATH, 'r', encoding='utf-8') as f:\n", "            labels = [line.strip() for line in f if line.strip()]\n", "        \n", "        for label in labels:\n", "            vocabulary.add(label.lower())\n", "            # Also add individual words from multi-word labels\n", "            words = label.lower().split()\n", "            for word in words:\n", "                if len(word) > 2:\n", "                    vocabulary.add(word)\n", "        \n", "        print(f\"✅ Loaded {len(vocabulary)} terms from controlled vocabulary ({self.config.DEFAULT_LABELS_PATH})\")\n", "        \n", "    except FileNotFoundError:\n", "        print(f\"⚠️ {self.config.DEFAULT_LABELS_PATH} not found. Using fallback vocabulary.\")\n", "        # Fallback vocabulary for demonstration\n", "        fallback_labels = [\n", "            'Logo', 'Brand Logo', 'Customer Logo', 'Footer', 'Rating', 'Slider',\n", "            'Video', 'Video Embeds', 'Embedded player', 'Video with controls',\n", "            'Heading', 'Main Heading', 'Page Heading', 'Section Heading', 'Sub Heading',\n", "            'Button', 'Primary Button', 'Secondary Button', 'Icon button', 'Ghost Button',\n", "            'Image', 'Standard Image', 'Interactive Image', 'Thumbnail Image',\n", "            'Navigation', 'Navigation Bars', 'Top Navigation Bar', 'Sidebar Navigation',\n", "            'Menu', 'Dropdown Menu', 'Context Menu', 'Hamburger Menu',\n", "            'Icon', 'Standard Icon', 'Interactive Icon', 'Status Icon', 'Social Icon',\n", "            'Form', 'Basic Form', 'Login Form', 'Signup Form', 'Multi-step Form',\n", "            'Card', 'Standard Card', 'Image Card', 'Testimonial Card',\n", "            'List', 'Ordered List', 'Unordered List', 'Bulleted List',\n", "            'Tab', 'Standard Tab', 'Icon Tab', 'Scrollable Tab Bar',\n", "            'Figure', '<PERSON>', 'Element', 'And', 'For', 'With', 'The'\n", "        ]\n", "        \n", "        for label in fallback_labels:\n", "            vocabulary.add(label.lower())\n", "            # Also add individual words from multi-word labels\n", "            words = label.lower().split()\n", "            for word in words:\n", "                if len(word) > 2:\n", "                    vocabulary.add(word)\n", "        \n", "        print(f\"✅ Loaded {len(vocabulary)} terms from fallback vocabulary\")\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error loading vocabulary: {e}\")\n", "        vocabulary = set(['video', 'heading', 'button', 'image', 'figure', 'page', 'element'])\n", "        print(f\"✅ Using minimal vocabulary: {len(vocabulary)} terms\")\n", "    \n", "    return vocabulary\n", "\n", "# Bind the controlled vocabulary loading method to the class\n", "UIElementProcessor._load_controlled_vocabulary = _load_controlled_vocabulary\n", "\n", "print(\"✅ Controlled vocabulary loading method added!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "processor_methods"}, "outputs": [], "source": ["# Add methods to UIElementProcessor class\n", "def process_and_store_data(self, coordinates_data, element_info_data):\n", "    \"\"\"Enhanced processing and storage of UI element data in separate vector databases\"\"\"\n", "    self.coordinates_data = coordinates_data\n", "    self.element_info_data = element_info_data\n", "\n", "    # Clear existing data\n", "    try:\n", "        self.coordinates_collection.delete()\n", "        self.dom_collection.delete()\n", "    except:\n", "        pass\n", "\n", "    # Store coordinates data for semantic search\n", "    self._store_coordinates_data()\n", "\n", "    # Store complete DOM data indexed by position\n", "    self._store_dom_data()\n", "\n", "    print(f\"✅ Stored {len(self.coordinates_data)} coordinate elements and {len(self.element_info_data)} DOM elements\")\n", "\n", "def _store_coordinates_data(self):\n", "    \"\"\"Store coordinates data in vector database for semantic search\"\"\"\n", "    coord_documents = []\n", "    coord_metadatas = []\n", "    coord_ids = []\n", "\n", "    for i, coord_item in enumerate(self.coordinates_data):\n", "        # Create searchable text for coordinates\n", "        coord_text = self._create_coordinate_search_text(coord_item, i)\n", "\n", "        # Create metadata for coordinates\n", "        coord_metadata = {\n", "            \"index\": i,\n", "            \"element_id\": f\"element_{i+1}\",\n", "            \"label\": coord_item[\"label\"],\n", "            \"x\": coord_item[\"coordinates\"][\"x\"],\n", "            \"y\": coord_item[\"coordinates\"][\"y\"],\n", "            \"width\": coord_item[\"coordinates\"][\"width\"],\n", "            \"height\": coord_item[\"coordinates\"][\"height\"]\n", "        }\n", "\n", "        coord_documents.append(coord_text)\n", "        coord_metadatas.append(coord_metadata)\n", "        coord_ids.append(f\"coord_{i}\")\n", "\n", "    # Store coordinates in ChromaDB\n", "    if coord_documents:\n", "        self.coordinates_collection.add(\n", "            documents=coord_documents,\n", "            metadatas=coord_metadatas,\n", "            ids=coord_ids\n", "        )\n", "        print(f\"✅ Stored {len(coord_documents)} coordinate elements for semantic search\")\n", "\n", "# Bind methods to the class\n", "UIElementProcessor.process_and_store_data = process_and_store_data\n", "UIElementProcessor._store_coordinates_data = _store_coordinates_data\n", "\n", "print(\"✅ UIElementProcessor methods added!\")"]}, {"cell_type": "markdown", "metadata": {"id": "sample_data"}, "source": ["## 📊 Data Loading from Configuration Files"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "load_data_from_files"}, "outputs": [], "source": ["# Load data from configuration file paths\n", "def load_data_from_config():\n", "    \"\"\"Load coordinates and element info data from configuration file paths\"\"\"\n", "    \n", "    print(\"📁 Loading data from configuration file paths...\")\n", "    print(f\"📍 Coordinates file: {config.DEFAULT_COORDINATES_PATH}\")\n", "    print(f\"🏗️ Element info file: {config.DEFAULT_ELEMENT_INFO_PATH}\")\n", "    print(f\"🏷️ Labels file: {config.DEFAULT_LABELS_PATH}\")\n", "    \n", "    coordinates_data = None\n", "    element_info_data = None\n", "    \n", "    try:\n", "        # Try to load coordinates.json\n", "        try:\n", "            with open(config.DEFAULT_COORDINATES_PATH, 'r', encoding='utf-8') as f:\n", "                coordinates_data = json.load(f)\n", "            print(f\"✅ Loaded coordinates data: {len(coordinates_data)} elements\")\n", "        except FileNotFoundError:\n", "            print(f\"⚠️ {config.DEFAULT_COORDINATES_PATH} not found. You can upload your own file below.\")\n", "        except Exception as e:\n", "            print(f\"❌ Error loading coordinates: {e}\")\n", "        \n", "        # Try to load element_info.json\n", "        try:\n", "            with open(config.DEFAULT_ELEMENT_INFO_PATH, 'r', encoding='utf-8') as f:\n", "                element_info_data = json.load(f)\n", "            print(f\"✅ Loaded element info data: {len(element_info_data)} elements\")\n", "        except FileNotFoundError:\n", "            print(f\"⚠️ {config.DEFAULT_ELEMENT_INFO_PATH} not found. You can upload your own file below.\")\n", "        except Exception as e:\n", "            print(f\"❌ Error loading element info: {e}\")\n", "        \n", "        # Try to load label.txt\n", "        try:\n", "            with open(config.DEFAULT_LABELS_PATH, 'r', encoding='utf-8') as f:\n", "                labels = [line.strip() for line in f if line.strip()]\n", "            print(f\"✅ Loaded controlled vocabulary: {len(labels)} labels\")\n", "        except FileNotFoundError:\n", "            print(f\"⚠️ {config.DEFAULT_LABELS_PATH} not found. Using fallback vocabulary.\")\n", "        except Exception as e:\n", "            print(f\"❌ Error loading labels: {e}\")\n", "        \n", "        return coordinates_data, element_info_data\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error in data loading: {e}\")\n", "        return None, None\n", "\n", "def create_fallback_data():\n", "    \"\"\"Create minimal fallback data for demonstration when files are not available\"\"\"\n", "    print(\"\\n🔄 Creating fallback demonstration data...\")\n", "    \n", "    fallback_coordinates = [\n", "        {\n", "            \"coordinates\": {\"x\": 949, \"y\": 385, \"width\": 626, \"height\": 330},\n", "            \"label\": \"Video\"\n", "        },\n", "        {\n", "            \"coordinates\": {\"x\": 323, \"y\": 451, \"width\": 602, \"height\": 128},\n", "            \"label\": \"Main Heading\"\n", "        },\n", "        {\n", "            \"coordinates\": {\"x\": 725, \"y\": 2666, \"width\": 447, \"height\": 90},\n", "            \"label\": \"Figure\"\n", "        }\n", "    ]\n", "    \n", "    fallback_element_info = {\n", "        \"element_1\": {\n", "            \"tag\": \"video\",\n", "            \"text\": \"\",\n", "            \"cssSelector\": \"video.Video_video__KYz0l\",\n", "            \"xpath\": \"//*[@id='__next']/div[1]/video[1]\",\n", "            \"src\": \"https://example.com/video.mp4\",\n", "            \"classes\": [\"Video_video__KYz0l\"],\n", "            \"attributes\": {\"autoplay\": \"\", \"muted\": \"\"},\n", "            \"computedStyle\": {\"display\": \"block\", \"width\": \"626px\"}\n", "        },\n", "        \"element_2\": {\n", "            \"tag\": \"h1\",\n", "            \"text\": \"Sample Heading Text\",\n", "            \"cssSelector\": \"h1.heading\",\n", "            \"xpath\": \"//*[@id='__next']/h1[1]\",\n", "            \"classes\": [\"heading\"],\n", "            \"attributes\": {\"class\": \"heading\"},\n", "            \"computedStyle\": {\"font-size\": \"64px\", \"color\": \"rgb(25, 25, 24)\"}\n", "        },\n", "        \"element_3\": {\n", "            \"tag\": \"figure\",\n", "            \"text\": \"Sample figure content\",\n", "            \"cssSelector\": \"figure.quote\",\n", "            \"xpath\": \"//*[@id='content']/figure[1]\",\n", "            \"classes\": [\"quote\"],\n", "            \"attributes\": {\"class\": \"quote\"},\n", "            \"computedStyle\": {\"display\": \"flex\", \"align-items\": \"center\"}\n", "        }\n", "    }\n", "    \n", "    print(f\"📊 Fallback coordinates: {len(fallback_coordinates)} elements\")\n", "    print(f\"🏗️ Fallback element info: {len(fallback_element_info)} elements\")\n", "    \n", "    return fallback_coordinates, fallback_element_info\n", "\n", "# Load data from configuration files\n", "coordinates_data, element_info_data = load_data_from_config()\n", "\n", "# Use fallback data if files are not available\n", "if coordinates_data is None or element_info_data is None:\n", "    print(\"\\n⚠️ Configuration files not found. Using fallback data for demonstration.\")\n", "    print(\"💡 You can upload your own files using the upload interface below.\")\n", "    coordinates_data, element_info_data = create_fallback_data()\n", "\n", "print(f\"\\n✅ Data loading complete!\")\n", "print(f\"📊 Final coordinates: {len(coordinates_data)} elements\")\n", "print(f\"🏗️ Final element info: {len(element_info_data)} elements\")"]}, {"cell_type": "markdown", "metadata": {"id": "additional_methods"}, "source": ["## 🔧 Additional Methods and UI Analyzer"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "helper_methods"}, "outputs": [], "source": ["# Add remaining methods to UIElementProcessor\n", "def _create_coordinate_search_text(self, coord_item, index):\n", "    \"\"\"Create searchable text for coordinate data\"\"\"\n", "    text_parts = [\n", "        f\"Label: {coord_item['label']}\",\n", "        f\"Element type: {coord_item['label'].lower()}\",\n", "        f\"UI element: {coord_item['label']}\",\n", "        f\"Position: x={coord_item['coordinates']['x']}, y={coord_item['coordinates']['y']}\",\n", "        f\"Size: {coord_item['coordinates']['width']}x{coord_item['coordinates']['height']}\",\n", "        f\"Index: {index}\",\n", "        f\"Element number: {index + 1}\"\n", "    ]\n", "    return \" | \".join(text_parts)\n", "\n", "def _store_dom_data(self):\n", "    \"\"\"Store complete DOM data indexed by position with enhanced metadata\"\"\"\n", "    dom_documents = []\n", "    dom_metadatas = []\n", "    dom_ids = []\n", "\n", "    for element_key, element_info in self.element_info_data.items():\n", "        # Extract index from element_key (e.g., \"element_1\" -> 0)\n", "        index = int(element_key.split('_')[1]) - 1\n", "\n", "        # Create comprehensive DOM text for embedding\n", "        dom_text = self._create_dom_search_text(element_info, index)\n", "\n", "        # Create comprehensive metadata including all DOM information\n", "        dom_metadata = {\n", "            \"index\": index,\n", "            \"element_id\": element_key,\n", "            \"tag\": element_info.get(\"tag\", \"\"),\n", "            \"text\": element_info.get(\"text\", \"\"),\n", "            \"css_selector\": element_info.get(\"cssSelector\", \"\"),\n", "            \"xpath\": element_info.get(\"xpath\", \"\"),\n", "            \"src\": element_info.get(\"src\", \"\"),\n", "            \"href\": element_info.get(\"href\", \"\"),\n", "            \"classes\": json.dumps(element_info.get(\"classes\", [])),\n", "            \"attributes\": json.dumps(element_info.get(\"attributes\", {})),\n", "            # Store the entire element_info as JSON for complete retrieval\n", "            \"full_element_data\": json.dumps(element_info),\n", "            # Add coordinate information if available\n", "            \"has_coordinates\": index < len(self.coordinates_data),\n", "            \"coordinate_label\": self.coordinates_data[index][\"label\"] if index < len(self.coordinates_data) else \"\",\n", "            \"coordinate_x\": self.coordinates_data[index][\"coordinates\"][\"x\"] if index < len(self.coordinates_data) else 0,\n", "            \"coordinate_y\": self.coordinates_data[index][\"coordinates\"][\"y\"] if index < len(self.coordinates_data) else 0,\n", "            \"coordinate_width\": self.coordinates_data[index][\"coordinates\"][\"width\"] if index < len(self.coordinates_data) else 0,\n", "            \"coordinate_height\": self.coordinates_data[index][\"coordinates\"][\"height\"] if index < len(self.coordinates_data) else 0\n", "        }\n", "\n", "        dom_documents.append(dom_text)\n", "        dom_metadatas.append(dom_metadata)\n", "        dom_ids.append(f\"dom_{index}\")\n", "\n", "    # Store DOM data in ChromaDB\n", "    if dom_documents:\n", "        self.dom_collection.add(\n", "            documents=dom_documents,\n", "            metadatas=dom_metadatas,\n", "            ids=dom_ids\n", "        )\n", "        print(f\"✅ Stored {len(dom_documents)} DOM elements with complete information\")\n", "\n", "def _create_dom_search_text(self, element_info, index):\n", "    \"\"\"Create comprehensive searchable text for DOM data\"\"\"\n", "    text_parts = [\n", "        f\"Tag: {element_info.get('tag', '')}\",\n", "        f\"Text content: {element_info.get('text', '')}\",\n", "        f\"CSS classes: {' '.join(element_info.get('classes', []))}\",\n", "        f\"CSS selector: {element_info.get('cssSelector', '')}\",\n", "        f\"XPath: {element_info.get('xpath', '')}\",\n", "        f\"Index: {index}\",\n", "        f\"Element number: {index + 1}\"\n", "    ]\n", "\n", "    # Add attributes if available\n", "    if 'attributes' in element_info:\n", "        attrs = element_info['attributes']\n", "        if attrs:\n", "            attr_text = \", \".join([f\"{k}={v or ''}\" for k, v in attrs.items()])\n", "            text_parts.append(f\"Attributes: {attr_text}\")\n", "\n", "    return \" | \".join(text_parts)\n", "\n", "# Bind additional methods to the class\n", "UIElementProcessor._create_coordinate_search_text = _create_coordinate_search_text\n", "UIElementProcessor._store_dom_data = _store_dom_data\n", "UIElementProcessor._create_dom_search_text = _create_dom_search_text\n", "\n", "print(\"✅ Additional methods added to UIElementProcessor!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "fix_none_values"}, "outputs": [], "source": ["# Fix None values in metadata for ChromaDB compatibility\n", "def _sanitize_metadata(self, metadata):\n", "    \"\"\"Sanitize metadata to ensure no None values for ChromaDB compatibility\"\"\"\n", "    sanitized = {}\n", "    for key, value in metadata.items():\n", "        if value is None:\n", "            sanitized[key] = \"\"\n", "        elif isinstance(value, (int, float, bool)):\n", "            sanitized[key] = value\n", "        else:\n", "            sanitized[key] = str(value)\n", "    return sanitized\n", "\n", "def _store_dom_data_fixed(self):\n", "    \"\"\"Store complete DOM element information with None value handling\"\"\"\n", "    dom_documents = []\n", "    dom_metadatas = []\n", "    dom_ids = []\n", "\n", "    for index, (element_key, element_info) in enumerate(self.element_info_data.items()):\n", "        # Create comprehensive DOM text for embedding\n", "        dom_text = self._create_dom_search_text(element_info, index)\n", "\n", "        # Create comprehensive metadata - handle None values\n", "        dom_metadata = {\n", "            \"index\": int(index),\n", "            \"element_id\": str(element_key),\n", "            \"tag\": str(element_info.get(\"tag\") or \"\"),\n", "            \"text\": str(element_info.get(\"text\") or \"\"),\n", "            \"css_selector\": str(element_info.get(\"cssSelector\") or \"\"),\n", "            \"xpath\": str(element_info.get(\"xpath\") or \"\"),\n", "            \"src\": str(element_info.get(\"src\") or \"\"),\n", "            \"href\": str(element_info.get(\"href\") or \"\"),\n", "            \"classes\": json.dumps(element_info.get(\"classes\") or []),\n", "            \"attributes\": json.dumps(element_info.get(\"attributes\") or {}),\n", "            \"full_element_data\": json.dumps(element_info),\n", "            \"has_coordinates\": bool(index < len(self.coordinates_data)),\n", "            \"coordinate_label\": str(self.coordinates_data[index][\"label\"] if index < len(self.coordinates_data) else \"\"),\n", "            \"coordinate_x\": int(self.coordinates_data[index][\"coordinates\"][\"x\"] if index < len(self.coordinates_data) else 0),\n", "            \"coordinate_y\": int(self.coordinates_data[index][\"coordinates\"][\"y\"] if index < len(self.coordinates_data) else 0),\n", "            \"coordinate_width\": int(self.coordinates_data[index][\"coordinates\"][\"width\"] if index < len(self.coordinates_data) else 0),\n", "            \"coordinate_height\": int(self.coordinates_data[index][\"coordinates\"][\"height\"] if index < len(self.coordinates_data) else 0)\n", "        }\n", "\n", "        # Sanitize metadata to ensure no None values\n", "        dom_metadata = self._sanitize_metadata(dom_metadata)\n", "\n", "        dom_documents.append(dom_text)\n", "        dom_metadatas.append(dom_metadata)\n", "        dom_ids.append(f\"dom_{index}\")\n", "\n", "    # Store DOM data in ChromaDB\n", "    if dom_documents:\n", "        self.dom_collection.add(\n", "            documents=dom_documents,\n", "            metadatas=dom_metadatas,\n", "            ids=dom_ids\n", "        )\n", "        print(f\"✅ Stored {len(dom_documents)} DOM elements with sanitized metadata\")\n", "\n", "# Replace the original method with the fixed version\n", "UIElementProcessor._sanitize_metadata = _sanitize_metadata\n", "UIElementProcessor._store_dom_data = _store_dom_data_fixed\n", "\n", "print(\"✅ Fixed None value handling for ChromaDB compatibility!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "intelligent_label_detection"}, "outputs": [], "source": ["# Add intelligent label detection methods\n", "def _identify_target_label(self, query_lower):\n", "    \"\"\"Intelligently identify the specific UI element label the user is asking about\"\"\"\n", "    # Define query patterns that indicate the user is asking about a specific element\n", "    element_patterns = [\n", "        r'what is the (\\w+)',\n", "        r'where is the (\\w+)',\n", "        r'show me the (\\w+)',\n", "        r'find the (\\w+)',\n", "        r'tell me about the (\\w+)',\n", "        r'(\\w+) element',\n", "        r'the (\\w+) on the page',\n", "        r'(\\w+) attributes',\n", "        r'(\\w+) properties',\n", "        r'css classes for the (\\w+)',\n", "        r'xpath for the (\\w+)',\n", "    ]\n", "    \n", "    # Try to extract the target element from query patterns\n", "    for pattern in element_patterns:\n", "        matches = re.findall(pattern, query_lower)\n", "        for match in matches:\n", "            # Check if this match exists in our controlled vocabulary\n", "            if match in self.controlled_vocabulary:\n", "                return match\n", "            \n", "            # Check for fuzzy matches\n", "            fuzzy_match = self._find_best_fuzzy_match(match)\n", "            if fuzzy_match:\n", "                print(f\"🔍 Fuzzy match: '{match}' → '{fuzzy_match}'\")\n", "                return fuzzy_match\n", "    \n", "    # Try to find multi-word labels that appear in the query\n", "    for label in self.controlled_vocabulary:\n", "        if len(label.split()) > 1:  # Multi-word labels\n", "            if label in query_lower:\n", "                return label\n", "    \n", "    return None\n", "\n", "def _find_best_fuzzy_match(self, word):\n", "    \"\"\"Find the best fuzzy match for a word in the controlled vocabulary\"\"\"\n", "    if len(word) < 3:  # Skip very short words\n", "        return None\n", "        \n", "    best_match = None\n", "    best_ratio = 0.0\n", "    \n", "    for vocab_term in self.controlled_vocabulary:\n", "        # Only consider single words for fuzzy matching\n", "        if len(vocab_term.split()) == 1:\n", "            ratio = SequenceMatcher(None, word, vocab_term).ratio()\n", "            if ratio > best_ratio and ratio > 0.8:  # 80% similarity threshold\n", "                best_ratio = ratio\n", "                best_match = vocab_term\n", "    \n", "    return best_match\n", "\n", "# Enhanced keyword extraction with intelligent target detection\n", "def _extract_keywords_enhanced(self, query):\n", "    \"\"\"Enhanced keyword extraction using intelligent target label detection\"\"\"\n", "    if not self.controlled_vocabulary:\n", "        # Fallback to original method if no controlled vocabulary\n", "        words = re.findall(r'\\b\\w+\\b', query.lower())\n", "        stop_words = {'the', 'is', 'at', 'which', 'on', 'a', 'an', 'and', 'or', 'but', 'in', 'with', 'to', 'for', 'of', 'as', 'by', 'what', 'where', 'how', 'when', 'why', 'who'}\n", "        return [word for word in words if word not in stop_words and len(word) > 2]\n", "    \n", "    query_lower = query.lower()\n", "    \n", "    # Step 1: Identify the target UI element label\n", "    target_label = self._identify_target_label(query_lower)\n", "    \n", "    if target_label:\n", "        print(f\"🎯 Target UI element identified: '{target_label}'\")\n", "        return [target_label]\n", "    \n", "    # Step 2: Fallback to general keyword extraction if no specific target found\n", "    matched_keywords = []\n", "    words = re.findall(r'\\b\\w+\\b', query_lower)\n", "    for word in words:\n", "        if word in self.controlled_vocabulary and word not in matched_keywords:\n", "            matched_keywords.append(word)\n", "            print(f\"🎯 Vocabulary match found: '{word}'\")\n", "    \n", "    print(f\"🔍 Final matched keywords from controlled vocabulary: {matched_keywords}\")\n", "    return matched_keywords\n", "\n", "# Bind intelligent detection methods to the class\n", "UIElementProcessor._identify_target_label = _identify_target_label\n", "UIElementProcessor._find_best_fuzzy_match = _find_best_fuzzy_match\n", "UIElementProcessor._extract_keywords = _extract_keywords_enhanced\n", "\n", "print(\"✅ Intelligent label detection methods added!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "enhanced_coordinate_search"}, "outputs": [], "source": ["# Add enhanced coordinate search with target label detection\n", "def _search_coordinates_enhanced(self, query, top_k=3):\n", "    \"\"\"Enhanced coordinate search using intelligent label detection\"\"\"\n", "    \n", "    # First, try to identify the target label from the query\n", "    target_label = self._identify_target_label(query.lower())\n", "    \n", "    if target_label:\n", "        # Search specifically for the identified target label\n", "        coordinate_results = self._search_by_target_label(target_label)\n", "        if coordinate_results:\n", "            print(f\"🎯 Found exact match for target label: '{target_label}'\")\n", "            return coordinate_results\n", "    \n", "    # Fallback to general search if no specific target found\n", "    return self._search_coordinates_general(query, top_k)\n", "\n", "def _search_by_target_label(self, target_label):\n", "    \"\"\"Search for coordinates that match the specific target label\"\"\"\n", "    coordinate_results = []\n", "    \n", "    # Get all items from the collection\n", "    all_items = self.coordinates_collection.get()\n", "    \n", "    for i, doc in enumerate(all_items['documents']):\n", "        metadata = all_items['metadatas'][i]\n", "        label = metadata.get('label', '').lower()\n", "        \n", "        # Check for exact label match or partial match\n", "        if (target_label == label or \n", "            target_label in label or \n", "            label in target_label or\n", "            self._labels_are_similar(target_label, label)):\n", "            \n", "            coordinate_results.append({\n", "                \"document\": doc,\n", "                \"metadata\": metadata,\n", "                \"distance\": 0.1,  # High confidence for exact matches\n", "                \"match_type\": \"target_label\"\n", "            })\n", "            print(f\"📍 Target label match: '{target_label}' → '{label}'\")\n", "    \n", "    return coordinate_results\n", "\n", "def _labels_are_similar(self, label1, label2):\n", "    \"\"\"Check if two labels are similar enough to be considered a match\"\"\"\n", "    # Handle common variations\n", "    variations = {\n", "        'video': ['video embeds', 'embedded player', 'video with controls'],\n", "        'heading': ['main heading', 'page heading', 'section heading', 'sub heading', 'card heading'],\n", "        'button': ['primary button', 'secondary button', 'icon button', 'ghost button'],\n", "        'image': ['standard image', 'interactive image', 'thumbnail image', 'image with caption'],\n", "        'navigation': ['navigation bars', 'top navigation bar', 'sidebar navigation', 'bottom navigation'],\n", "        'menu': ['dropdown menu', 'context menu', 'hamburger menu'],\n", "        'icon': ['standard icon', 'interactive icon', 'status icon', 'social icon'],\n", "        'form': ['basic form', 'login form', 'signup form', 'multi-step form'],\n", "        'card': ['standard card', 'image card', 'testimonial card'],\n", "        'list': ['ordered list', 'unordered list', 'bulleted list'],\n", "        'tab': ['standard tab', 'icon tab', 'scrollable tab bar'],\n", "        'slider': ['continuous slider', 'discrete slider', 'range slider'],\n", "        'checkbox': ['basic checkbox', 'tri-state checkbox', 'disabled checkbox'],\n", "        'toggle': ['basic toggle', 'labeled toggle', 'on/off toggle'],\n", "        'dropdown': ['multi-select dropdown', 'searchable dropdown', 'disabled dropdown']\n", "    }\n", "    \n", "    # Check if label1 is a base form of label2 or vice versa\n", "    for base, variants in variations.items():\n", "        if label1 == base and any(variant in label2 for variant in variants):\n", "            return True\n", "        if label2 == base and any(variant in label1 for variant in variants):\n", "            return True\n", "    \n", "    return False\n", "\n", "def _search_coordinates_general(self, query, top_k):\n", "    \"\"\"General coordinate search using vector search or text matching\"\"\"\n", "    # Fallback to simple text matching for <PERSON><PERSON>\n", "    all_items = self.coordinates_collection.get()\n", "    coordinate_results = []\n", "\n", "    query_lower = query.lower()\n", "    for i, doc in enumerate(all_items['documents']):\n", "        metadata = all_items['metadatas'][i]\n", "        # Simple keyword matching\n", "        if any(keyword in doc.lower() for keyword in query_lower.split()):\n", "            coordinate_results.append({\n", "                \"document\": doc,\n", "                \"metadata\": metadata,\n", "                \"distance\": 0.5,  # Default similarity score\n", "                \"match_type\": \"text_matching\"\n", "            })\n", "\n", "    # Limit results\n", "    coordinate_results = coordinate_results[:top_k]\n", "    return coordinate_results\n", "\n", "# Bind enhanced search methods to the class\n", "UIElementProcessor._search_coordinates = _search_coordinates_enhanced\n", "UIElementProcessor._search_by_target_label = _search_by_target_label\n", "UIElementProcessor._labels_are_similar = _labels_are_similar\n", "UIElementProcessor._search_coordinates_general = _search_coordinates_general\n", "\n", "print(\"✅ Enhanced coordinate search with intelligent target detection added!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ui_analyzer_class"}, "outputs": [], "source": ["class UIAnalyzer:\n", "    \"\"\"Enhanced UI analyzer using Gemini AI with comprehensive vector database workflow\"\"\"\n", "\n", "    def __init__(self):\n", "        self.config = config\n", "        self.processor = UIElementProcessor()\n", "\n", "        # Configure Gemini\n", "        genai.configure(api_key=self.config.GOOGLE_API_KEY)\n", "        self.model = genai.GenerativeModel(self.config.MODEL_NAME)\n", "        print(\"✅ UIAnalyzer initialized with enhanced vector database workflow!\")\n", "\n", "    def analyze_query(self, query: str) -> str:\n", "        \"\"\"\n", "        Enhanced query analysis following the complete workflow:\n", "        1. Query Understanding: Detect keywords\n", "        2. Vector Search in Coordinates DB: Search for labels\n", "        3. Index Extraction: Identify indices\n", "        4. DOM Data Retrieval: Get corresponding DOM data\n", "        5. Context Injection: Store DOM data and pass as context\n", "        6. Coordinate Inclusion: Include position information\n", "        7. Model Response: Generate position-aware response\n", "        \"\"\"\n", "        print(f\"\\n🔍 Analyzing query: '{query}'\")\n", "        print(\"=\" * 50)\n", "\n", "        # Use the enhanced workflow\n", "        analysis_result = self.processor.analyze_query_and_retrieve_context(query)\n", "\n", "        # Generate response using Gemini with enhanced context\n", "        response = self._generate_enhanced_response(query, analysis_result)\n", "\n", "        return response\n", "\n", "    def _generate_enhanced_response(self, query: str, analysis_result: Dict[str, Any]) -> str:\n", "        \"\"\"Generate response using Gemini with enhanced context from the workflow\"\"\"\n", "\n", "        # Extract information from analysis result\n", "        keywords = analysis_result[\"keywords\"]\n", "        combined_context = analysis_result[\"combined_context\"]\n", "        coordinate_matches = analysis_result[\"coordinate_matches\"]\n", "        dom_context = analysis_result[\"dom_context\"]\n", "\n", "        # Create enhanced prompt with workflow information\n", "        prompt = f\"\"\"\n", "You are an expert UI/UX analyst with access to comprehensive webpage analysis data. You have analyzed a user's question using an advanced workflow that includes:\n", "\n", "1. ✅ Query Understanding: Detected keywords: {', '.join(keywords)}\n", "2. ✅ Vector Search: Found {len(coordinate_matches)} relevant UI elements\n", "3. ✅ Index Extraction: Retrieved {len(dom_context)} complete DOM elements\n", "4. ✅ Context Integration: Combined coordinate and DOM information\n", "5. ✅ Position Awareness: Included exact pixel coordinates\n", "\n", "User Question: {query}\n", "\n", "COMPREHENSIVE UI ELEMENT ANALYSIS:\n", "{combined_context}\n", "\n", "ANALYSIS WORKFLOW SUMMARY:\n", "- Keywords detected: {', '.join(keywords)}\n", "- Elements found: {len(coordinate_matches)} coordinate matches\n", "- DOM data retrieved: {len(dom_context)} complete elements\n", "- Position data: Exact pixel coordinates included\n", "\n", "Please provide a comprehensive, position-aware response that:\n", "1. Directly addresses the user's question using the detected keywords\n", "2. References specific UI elements by their exact positions and properties\n", "3. Explains the functionality, purpose, and technical details of the elements\n", "4. Provides precise coordinate information (x, y, width, height)\n", "5. Includes relevant DOM information (HTML tags, CSS selectors, XPath)\n", "6. Uses clear, user-friendly language while being technically accurate\n", "7. Mentions how the element relates to the overall page structure\n", "\n", "Response:\n", "\"\"\"\n", "\n", "        try:\n", "            response = self.model.generate_content(prompt)\n", "            return response.text\n", "        except Exception as e:\n", "            return f\"❌ Error generating response: {str(e)}\"\n", "\n", "    def initialize_data(self, coordinates_data, element_info_data):\n", "        \"\"\"Initialize the enhanced system with UI data\"\"\"\n", "        print(f\"\\n📊 Initializing enhanced vector database system...\")\n", "        print(f\"📍 Coordinates: {len(coordinates_data)} elements\")\n", "        print(f\"🏗️ Element info: {len(element_info_data)} elements\")\n", "\n", "        self.processor.process_and_store_data(coordinates_data, element_info_data)\n", "        print(\"✅ Enhanced UI data initialized successfully!\")\n", "\n", "print(\"✅ UIAnalyzer class defined!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "workflow_methods"}, "outputs": [], "source": ["# Add workflow methods to UIElementProcessor\n", "def analyze_query_and_retrieve_context(self, query: str, top_k: int = None) -> Dict[str, Any]:\n", "    \"\"\"\n", "    Enhanced query analysis following the specified workflow:\n", "    1. Query Understanding: Detect keywords\n", "    2. Vector Search in Coordinates DB: Search for labels\n", "    3. Index Extraction: Identify indices\n", "    4. DOM Data Retrieval: Get corresponding DOM data\n", "    5. Context Injection: Combine coordinate and DOM information\n", "    \"\"\"\n", "    if top_k is None:\n", "        top_k = self.config.TOP_K_RESULTS\n", "\n", "    # Step 1: Query Understanding - Extract keywords\n", "    keywords = self._extract_keywords(query)\n", "    print(f\"🔍 Detected keywords: {keywords}\")\n", "\n", "    # Step 2: Vector Search in Coordinates DB\n", "    coordinate_results = self._search_coordinates(query, top_k)\n", "    print(f\"📍 Found {len(coordinate_results)} relevant coordinate matches\")\n", "\n", "    # Step 3: Index Extraction\n", "    relevant_indices = self._extract_indices_from_coordinates(coordinate_results)\n", "    print(f\"📊 Extracted indices: {relevant_indices}\")\n", "\n", "    # Step 4: DOM Data Retrieval using indices\n", "    dom_context = self._retrieve_dom_data_by_indices(relevant_indices)\n", "    print(f\"🏗️ Retrieved DOM data for {len(dom_context)} elements\")\n", "\n", "    # Step 5: Context Injection - Combine coordinate and DOM information\n", "    combined_context = self._combine_coordinate_and_dom_context(coordinate_results, dom_context)\n", "\n", "    return {\n", "        \"keywords\": keywords,\n", "        \"coordinate_matches\": coordinate_results,\n", "        \"relevant_indices\": relevant_indices,\n", "        \"dom_context\": dom_context,\n", "        \"combined_context\": combined_context,\n", "        \"query\": query\n", "    }\n", "\n", "def _extract_keywords(self, query: str) -> List[str]:\n", "    \"\"\"Extract relevant keywords from the user query\"\"\"\n", "    # Convert to lowercase and split\n", "    words = re.findall(r'\\b\\w+\\b', query.lower())\n", "\n", "    # Filter out common stop words\n", "    stop_words = {'the', 'is', 'at', 'which', 'on', 'a', 'an', 'and', 'or', 'but', 'in', 'with', 'to', 'for', 'of', 'as', 'by', 'what', 'where', 'how', 'when', 'why', 'who'}\n", "    keywords = [word for word in words if word not in stop_words and len(word) > 2]\n", "\n", "    return keywords\n", "\n", "def _search_coordinates(self, query: str, top_k: int) -> List[Dict[str, Any]]:\n", "    \"\"\"Search for relevant coordinates using vector search\"\"\"\n", "    if self.embeddings is not None:\n", "        # Use vector search if embeddings are available\n", "        results = self.coordinates_collection.query(\n", "            query_texts=[query],\n", "            n_results=top_k\n", "        )\n", "\n", "        coordinate_results = []\n", "        if results['documents'] and results['documents'][0]:\n", "            for i, doc in enumerate(results['documents'][0]):\n", "                metadata = results['metadatas'][0][i]\n", "                coordinate_results.append({\n", "                    \"document\": doc,\n", "                    \"metadata\": metadata,\n", "                    \"distance\": results['distances'][0][i] if 'distances' in results else None\n", "                })\n", "    else:\n", "        # Fallback to simple text matching\n", "        all_items = self.coordinates_collection.get()\n", "        coordinate_results = []\n", "\n", "        query_lower = query.lower()\n", "        for i, doc in enumerate(all_items['documents']):\n", "            metadata = all_items['metadatas'][i]\n", "            # Simple keyword matching\n", "            if any(keyword in doc.lower() for keyword in query_lower.split()):\n", "                coordinate_results.append({\n", "                    \"document\": doc,\n", "                    \"metadata\": metadata,\n", "                    \"distance\": 0.5  # Default similarity score\n", "                })\n", "\n", "        # Limit results\n", "        coordinate_results = coordinate_results[:top_k]\n", "\n", "    return coordinate_results\n", "\n", "# Bind workflow methods to the class\n", "UIElementProcessor.analyze_query_and_retrieve_context = analyze_query_and_retrieve_context\n", "UIElementProcessor._extract_keywords = _extract_keywords\n", "UIElementProcessor._search_coordinates = _search_coordinates\n", "\n", "print(\"✅ Workflow methods added to UIElementProcessor!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "remaining_methods"}, "outputs": [], "source": ["# Add remaining helper methods\n", "def _extract_indices_from_coordinates(self, coordinate_results: List[Dict[str, Any]]) -> List[int]:\n", "    \"\"\"Extract indices from coordinate search results\"\"\"\n", "    indices = []\n", "    for result in coordinate_results:\n", "        metadata = result[\"metadata\"]\n", "        if \"index\" in metadata:\n", "            indices.append(metadata[\"index\"])\n", "    return list(set(indices))  # Remove duplicates\n", "\n", "def _retrieve_dom_data_by_indices(self, indices: List[int]) -> List[Dict[str, Any]]:\n", "    \"\"\"Retrieve complete DOM data using the extracted indices\"\"\"\n", "    dom_context = []\n", "\n", "    for index in indices:\n", "        element_key = f\"element_{index + 1}\"\n", "        if element_key in self.element_info_data:\n", "            # Get the complete DOM information\n", "            element_info = self.element_info_data[element_key]\n", "\n", "            # Also get coordinate information for this index\n", "            coordinate_info = None\n", "            if index < len(self.coordinates_data):\n", "                coordinate_info = self.coordinates_data[index]\n", "\n", "            dom_context.append({\n", "                \"index\": index,\n", "                \"element_key\": element_key,\n", "                \"element_info\": element_info,\n", "                \"coordinate_info\": coordinate_info\n", "            })\n", "\n", "    return dom_context\n", "\n", "def _combine_coordinate_and_dom_context(self, coordinate_results: List[Dict[str, Any]], dom_context: List[Dict[str, Any]]) -> str:\n", "    \"\"\"Combine coordinate and DOM information into a comprehensive context\"\"\"\n", "    context_parts = []\n", "\n", "    # Create a mapping of indices to DOM data for easy lookup\n", "    dom_by_index = {item[\"index\"]: item for item in dom_context}\n", "\n", "    for coord_result in coordinate_results:\n", "        coord_metadata = coord_result[\"metadata\"]\n", "        index = coord_metadata.get(\"index\", -1)\n", "\n", "        if index in dom_by_index:\n", "            dom_item = dom_by_index[index]\n", "            element_info = dom_item[\"element_info\"]\n", "\n", "            # Extract key computed style properties for better context\n", "            computed_style = element_info.get('computedStyle', {})\n", "            key_styles = {}\n", "            important_style_props = ['display', 'position', 'width', 'height', 'color', 'background-color',\n", "                                   'font-size', 'font-weight', 'border-radius', 'opacity', 'z-index']\n", "            for prop in important_style_props:\n", "                if prop in computed_style:\n", "                    key_styles[prop] = computed_style[prop]\n", "\n", "            context_part = f\"\"\"\n", "Element {index + 1} ({coord_metadata['label']}):\n", "POSITION & SIZE:\n", "- Coordinates: ({coord_metadata['x']}, {coord_metadata['y']})\n", "- Dimensions: {coord_metadata['width']}px × {coord_metadata['height']}px\n", "- Area: {coord_metadata['width'] * coord_metadata['height']} square pixels\n", "\n", "DOM STRUCTURE:\n", "- HTML Tag: <{element_info.get('tag', 'unknown')}>\n", "- Text Content: \"{element_info.get('text', 'No text content')}\"\n", "- CSS Selector: {element_info.get('cssSelector', 'N/A')}\n", "- XPath: {element_info.get('xpath', 'N/A')}\n", "\n", "STYLING & ATTRIBUTES:\n", "- CSS Classes: {', '.join(element_info.get('classes', [])) or 'None'}\n", "- Key Attributes: {', '.join([f\"{k}='{v}'\" for k, v in element_info.get('attributes', {}).items() if v]) or 'None'}\n", "- Important Styles: {', '.join([f\"{k}: {v}\" for k, v in key_styles.items()]) or 'Default styles'}\n", "\n", "FUNCTIONALITY:\n", "- Source URL: {element_info.get('src', 'N/A')}\n", "- Link Target: {element_info.get('href', 'N/A')}\n", "- Inline Styles: {element_info.get('inlineStyle', 'None')}\n", "\"\"\"\n", "            context_parts.append(context_part.strip())\n", "\n", "    return \"\\n\\n\".join(context_parts)\n", "\n", "# Bind remaining methods to the class\n", "UIElementProcessor._extract_indices_from_coordinates = _extract_indices_from_coordinates\n", "UIElementProcessor._retrieve_dom_data_by_indices = _retrieve_dom_data_by_indices\n", "UIElementProcessor._combine_coordinate_and_dom_context = _combine_coordinate_and_dom_context\n", "\n", "print(\"✅ All helper methods added to UIElementProcessor!\")"]}, {"cell_type": "markdown", "metadata": {"id": "initialization"}, "source": ["## 🚀 System Initialization"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "initialize_system"}, "outputs": [], "source": ["# Initialize the UI Analyzer with data from configuration files\n", "print(\"🚀 Initializing Enhanced UI Element Analyzer\")\n", "print(\"=\" * 60)\n", "\n", "# Create analyzer instance\n", "analyzer = UIAnalyzer()\n", "\n", "# Initialize with data loaded from configuration files\n", "analyzer.initialize_data(coordinates_data, element_info_data)\n", "\n", "print(\"\\n🎉 System ready for queries!\")\n", "print(\"\\n📋 Available UI elements:\")\n", "for i, coord in enumerate(coordinates_data):\n", "    print(f\"  {i+1}. {coord['label']} at ({coord['coordinates']['x']}, {coord['coordinates']['y']})\")\n", "\n", "print(\"\\n💡 Data Source Information:\")\n", "print(f\"📍 Coordinates loaded from: {config.DEFAULT_COORDINATES_PATH}\")\n", "print(f\"🏗️ Element info loaded from: {config.DEFAULT_ELEMENT_INFO_PATH}\")\n", "print(f\"🏷️ Labels loaded from: {config.DEFAULT_LABELS_PATH}\")\n", "print(\"\\n📁 To use your own data, upload files using the interface below.\")"]}, {"cell_type": "markdown", "metadata": {"id": "interactive_interface"}, "source": ["## 💬 Interactive Query Interface"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "query_interface"}, "outputs": [], "source": ["# Interactive query interface\n", "def create_query_interface():\n", "    \"\"\"Create an interactive interface for querying UI elements\"\"\"\n", "    \n", "    # Example queries showcasing intelligent label detection\n", "    example_queries = [\n", "        \"What is the video element on the page?\",\n", "        \"Where is the main heading shown on the page?\",\n", "        \"Tell me about the figure element and its location\",\n", "        \"What are the video attributes and properties?\",\n", "        \"Show me the CSS classes for the heading element\",\n", "        \"What is the XPath for the video element?\",\n", "        \"Find the button element\",\n", "        \"Show me the navigation bar\",\n", "        \"video properties and details\",\n", "        \"heading element information\"\n", "    ]\n", "    \n", "    # Create dropdown for example queries\n", "    query_dropdown = widgets.Dropdown(\n", "        options=[\"Select an example...\"] + example_queries,\n", "        value=\"Select an example...\",\n", "        description='Examples:',\n", "        style={'description_width': 'initial'},\n", "        layout=widgets.Layout(width='100%')\n", "    )\n", "    \n", "    # Create text area for custom queries\n", "    query_text = widgets.Textarea(\n", "        value='',\n", "        placeholder='Enter your question about UI elements...',\n", "        description='Your Query:',\n", "        style={'description_width': 'initial'},\n", "        layout=widgets.Layout(width='100%', height='100px')\n", "    )\n", "    \n", "    # Create analyze button\n", "    analyze_button = widgets.Button(\n", "        description='🔍 Analyze Query',\n", "        button_style='primary',\n", "        layout=widgets.Layout(width='200px')\n", "    )\n", "    \n", "    # Create output area\n", "    output_area = widgets.Output()\n", "    \n", "    def on_dropdown_change(change):\n", "        if change['new'] != \"Select an example...\":\n", "            query_text.value = change['new']\n", "    \n", "    def on_analyze_click(button):\n", "        with output_area:\n", "            clear_output(wait=True)\n", "            \n", "            query = query_text.value.strip()\n", "            if not query:\n", "                print(\"⚠️ Please enter a query first!\")\n", "                return\n", "            \n", "            try:\n", "                print(f\"🔍 Processing query: '{query}'\")\n", "                print(\"=\" * 60)\n", "                \n", "                # Analyze the query\n", "                response = analyzer.analyze_query(query)\n", "                \n", "                print(\"\\n🤖 AI Response:\")\n", "                print(\"=\" * 60)\n", "                print(response)\n", "                \n", "            except Exception as e:\n", "                print(f\"❌ Error processing query: {str(e)}\")\n", "    \n", "    # Bind events\n", "    query_dropdown.observe(on_dropdown_change, names='value')\n", "    analyze_button.on_click(on_analyze_click)\n", "    \n", "    # Display interface\n", "    display(widgets.VBox([\n", "        widgets.HTML(\"<h3>🔍 Enhanced UI Element Query Interface</h3>\"),\n", "        widgets.HTML(\"<p>Ask questions about UI elements and get detailed, position-aware responses!</p>\"),\n", "        query_dropdown,\n", "        query_text,\n", "        analyze_button,\n", "        output_area\n", "    ]))\n", "\n", "# Create and display the interface\n", "create_query_interface()"]}, {"cell_type": "markdown", "metadata": {"id": "system_message_classes"}, "source": ["## 🎯 System Message & Heuristic Evaluation Classes"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "system_message_processor"}, "outputs": [], "source": ["# System Message Processor\n", "class SystemMessageProcessor:\n", "    \"\"\"Processes system messages to determine analysis workflows and context.\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.current_system_message = \"\"\n", "        self.analysis_context = {}\n", "        self.workflow_type = \"default\"\n", "        \n", "        # Define supported analysis workflows\n", "        self.supported_workflows = {\n", "            \"heuristic_evaluation\": {\n", "                \"keywords\": [\"heuristic\", \"evaluation\", \"usability\", \"nielsen\", \"principles\"],\n", "                \"description\": \"Comprehensive heuristic evaluation of UI elements\",\n", "                \"requires_element_filtering\": True,\n", "                \"output_format\": \"structured_violations\"\n", "            },\n", "            \"accessibility_assessment\": {\n", "                \"keywords\": [\"accessibility\", \"a11y\", \"wcag\", \"screen reader\", \"contrast\"],\n", "                \"description\": \"Accessibility compliance assessment\",\n", "                \"requires_element_filtering\": True,\n", "                \"output_format\": \"compliance_report\"\n", "            },\n", "            \"design_review\": {\n", "                \"keywords\": [\"design\", \"visual\", \"aesthetic\", \"layout\", \"typography\"],\n", "                \"description\": \"Visual design and layout review\",\n", "                \"requires_element_filtering\": <PERSON><PERSON><PERSON>,\n", "                \"output_format\": \"design_feedback\"\n", "            }\n", "        }\n", "    \n", "    def set_system_message(self, message):\n", "        \"\"\"Set and parse a new system message\"\"\"\n", "        self.current_system_message = message.strip()\n", "        \n", "        if not self.current_system_message:\n", "            return self._reset_to_default()\n", "        \n", "        # Determine workflow type\n", "        message_lower = message.lower()\n", "        workflow_type = \"default\"\n", "        \n", "        for wf_type, wf_info in self.supported_workflows.items():\n", "            if any(keyword in message_lower for keyword in wf_info[\"keywords\"]):\n", "                workflow_type = wf_type\n", "                break\n", "        \n", "        self.workflow_type = workflow_type\n", "        \n", "        # Generate instructions\n", "        if workflow_type == \"heuristic_evaluation\":\n", "            instructions = f\"\"\"\n", "HEURISTIC EVALUATION INSTRUCTIONS:\n", "1. Evaluate each UI element against established usability heuristics\n", "2. Focus on elements that are NOT labeled as \"Non-UI Element\" or \"Unknown Element\"\n", "3. For each element, check for violations of usability principles\n", "4. Report violations with severity levels and actionable recommendations\n", "5. If no violations are found, state explicitly: \"No heuristic violations found for this element.\"\n", "\"\"\"\n", "        else:\n", "            instructions = \"Provide general analysis based on the system message context.\"\n", "        \n", "        return {\n", "            \"workflow_type\": workflow_type,\n", "            \"instructions\": instructions,\n", "            \"requires_filtering\": self.supported_workflows.get(workflow_type, {}).get(\"requires_element_filtering\", False),\n", "            \"original_message\": message\n", "        }\n", "    \n", "    def _reset_to_default(self):\n", "        \"\"\"Reset to default analysis mode\"\"\"\n", "        self.workflow_type = \"default\"\n", "        return {\n", "            \"workflow_type\": \"default\",\n", "            \"instructions\": \"Provide general analysis of UI elements based on user queries.\",\n", "            \"requires_filtering\": <PERSON>als<PERSON>,\n", "            \"original_message\": \"\"\n", "        }\n", "    \n", "    def get_current_context(self):\n", "        \"\"\"Get current system message context\"\"\"\n", "        return {\n", "            \"system_message\": self.current_system_message,\n", "            \"workflow_type\": self.workflow_type,\n", "            \"requires_filtering\": self.supported_workflows.get(self.workflow_type, {}).get(\"requires_element_filtering\", False)\n", "        }\n", "    \n", "    def is_heuristic_evaluation_mode(self):\n", "        \"\"\"Check if currently in heuristic evaluation mode\"\"\"\n", "        return self.workflow_type == \"heuristic_evaluation\"\n", "\n", "print(\"✅ SystemMessageProcessor class defined!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "heuristic_evaluator"}, "outputs": [], "source": ["# Heuristic Evaluator\n", "class HeuristicEvaluator:\n", "    \"\"\"Comprehensive heuristic evaluator for UI elements based on established usability principles.\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.excluded_labels = [\"Non-UI Element\", \"Unknown Element\"]\n", "        \n", "        # Define heuristic evaluation criteria\n", "        self.heuristics = {\n", "            \"visibility_of_system_status\": \"The system should always keep users informed about what is going on\",\n", "            \"recognition_vs_recall\": \"Make elements, actions, and options visible\",\n", "            \"aesthetic_minimalist\": \"Dialogues should not contain irrelevant or rarely needed information\",\n", "            \"consistency_standards\": \"Users should not wonder whether different elements mean the same thing\",\n", "            \"error_prevention\": \"Prevent problems from occurring in the first place\"\n", "        }\n", "    \n", "    def should_evaluate_element(self, coordinate_data):\n", "        \"\"\"Determine if an element should be evaluated based on its label\"\"\"\n", "        label = coordinate_data.get(\"label\", \"\")\n", "        return label not in self.excluded_labels\n", "    \n", "    def evaluate_element(self, element_data, coordinate_data):\n", "        \"\"\"Evaluate a single UI element against all heuristics\"\"\"\n", "        evaluation_result = {\n", "            \"element_info\": {\n", "                \"index\": coordinate_data.get(\"index\", -1),\n", "                \"label\": coordinate_data.get(\"label\", \"Unknown\"),\n", "                \"coordinates\": coordinate_data.get(\"coordinates\", {}),\n", "                \"tag\": element_data.get(\"tag\", \"unknown\"),\n", "                \"text\": element_data.get(\"text\", \"\"),\n", "                \"css_selector\": element_data.get(\"cssSelector\", \"\"),\n", "                \"xpath\": element_data.get(\"xpath\", \"\")\n", "            },\n", "            \"violations\": [],\n", "            \"passed_checks\": [],\n", "            \"overall_score\": 0,\n", "            \"recommendations\": []\n", "        }\n", "        \n", "        # Check specific heuristics\n", "        violations = self._check_all_heuristics(element_data, coordinate_data)\n", "        evaluation_result[\"violations\"] = violations\n", "        \n", "        # Calculate score\n", "        total_checks = len(self.heuristics)\n", "        violations_count = len(violations)\n", "        passed_count = total_checks - violations_count\n", "        evaluation_result[\"overall_score\"] = (passed_count / total_checks) * 100 if total_checks > 0 else 0\n", "        \n", "        # Add passed checks\n", "        violated_heuristics = {v[\"heuristic\"] for v in violations}\n", "        for heuristic_name in self.heuristics.keys():\n", "            if heuristic_name not in violated_heuristics:\n", "                evaluation_result[\"passed_checks\"].append(heuristic_name)\n", "        \n", "        # Generate recommendations\n", "        evaluation_result[\"recommendations\"] = self._generate_recommendations(violations)\n", "        \n", "        return evaluation_result\n", "    \n", "    def _check_all_heuristics(self, element_data, coordinate_data):\n", "        \"\"\"Check all heuristics against an element\"\"\"\n", "        violations = []\n", "        label = coordinate_data.get(\"label\", \"\").lower()\n", "        text = element_data.get(\"text\", \"\")\n", "        coordinates = coordinate_data.get(\"coordinates\", {})\n", "        \n", "        # Check recognition vs recall\n", "        if \"button\" in label and not text.strip():\n", "            violations.append({\n", "                \"heuristic\": \"Recognition Rather Than Recall\",\n", "                \"violation\": \"Button without visible text label\",\n", "                \"reason\": \"Button element lacks descriptive text, users must recall its function\",\n", "                \"severity\": \"high\"\n", "            })\n", "        \n", "        # Check aesthetic and minimalist design\n", "        if \"button\" in label:\n", "            width = coordinates.get(\"width\", 0)\n", "            height = coordinates.get(\"height\", 0)\n", "            \n", "            if width < 44 or height < 44:  # Minimum touch target size\n", "                violations.append({\n", "                    \"heuristic\": \"Aesthetic and Minimalist Design\",\n", "                    \"violation\": \"Button too small for easy interaction\",\n", "                    \"reason\": f\"Button size ({width}x{height}px) is below recommended minimum (44x44px)\",\n", "                    \"severity\": \"medium\"\n", "                })\n", "        \n", "        # Check visibility of system status\n", "        if any(keyword in label for keyword in [\"button\", \"link\", \"input\"]):\n", "            computed_style = element_data.get(\"computedStyle\", {})\n", "            if \"cursor\" in computed_style and computed_style[\"cursor\"] == \"default\":\n", "                violations.append({\n", "                    \"heuristic\": \"Visibility of System Status\",\n", "                    \"violation\": \"Interactive element lacks cursor feedback\",\n", "                    \"reason\": f\"Element labeled '{coordinate_data.get('label')}' appears interactive but has default cursor\",\n", "                    \"severity\": \"medium\"\n", "                })\n", "        \n", "        return violations\n", "    \n", "    def _generate_recommendations(self, violations):\n", "        \"\"\"Generate actionable recommendations based on violations\"\"\"\n", "        recommendations = []\n", "        \n", "        for violation in violations:\n", "            if \"cursor feedback\" in violation[\"violation\"]:\n", "                recommendations.append(\"Add cursor: pointer to interactive elements\")\n", "            elif \"without visible text\" in violation[\"violation\"]:\n", "                recommendations.append(\"Add descriptive text labels to buttons\")\n", "            elif \"too small\" in violation[\"violation\"]:\n", "                recommendations.append(\"Increase button size to meet accessibility guidelines (minimum 44x44px)\")\n", "        \n", "        return list(set(recommendations))  # Remove duplicates\n", "\n", "print(\"✅ HeuristicEvaluator class defined!\")"]}, {"cell_type": "markdown", "metadata": {"id": "system_message_interface"}, "source": ["## 🎯 System Message Interface\n", "\n", "Set system messages to guide the analysis workflow:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "system_message_widget"}, "outputs": [], "source": ["# System Message Interface\n", "system_processor = SystemMessageProcessor()\n", "heuristic_evaluator = HeuristicEvaluator()\n", "\n", "# System message input\n", "system_message_input = widgets.Textarea(\n", "    value=\"\",\n", "    placeholder=\"Enter system message (e.g., 'You need to perform a heuristic evaluation of this web page. This is a landing page.')\",\n", "    description=\"System Message:\",\n", "    layout=widgets.Layout(width='100%', height='80px')\n", ")\n", "\n", "# Predefined system messages\n", "predefined_messages = {\n", "    \"Heuristic Evaluation\": \"You need to perform a heuristic evaluation of this web page. This is a landing page.\",\n", "    \"Accessibility Assessment\": \"Perform accessibility assessment focusing on WCAG compliance and screen reader compatibility.\",\n", "    \"Design Review\": \"Conduct design review focusing on visual hierarchy, typography, and aesthetic principles.\",\n", "    \"Clear System Message\": \"\"\n", "}\n", "\n", "predefined_dropdown = widgets.Dropdown(\n", "    options=list(predefined_messages.keys()),\n", "    value=\"Heuristic Evaluation\",\n", "    description=\"Quick Select:\",\n", "    layout=widgets.Layout(width='300px')\n", ")\n", "\n", "def on_predefined_change(change):\n", "    if change['type'] == 'change' and change['name'] == 'value':\n", "        system_message_input.value = predefined_messages[change['new']]\n", "\n", "predefined_dropdown.observe(on_predefined_change)\n", "\n", "# Set system message button\n", "set_system_button = widgets.Button(\n", "    description='🎯 Set System Message',\n", "    button_style='success',\n", "    layout=widgets.Layout(width='200px')\n", ")\n", "\n", "system_output = widgets.Output()\n", "\n", "def on_set_system_click(button):\n", "    with system_output:\n", "        clear_output(wait=True)\n", "        message = system_message_input.value.strip()\n", "        \n", "        if message:\n", "            result = system_processor.set_system_message(message)\n", "            print(f\"🎯 System message set successfully!\")\n", "            print(f\"📋 Workflow Type: {result['workflow_type'].replace('_', ' ').title()}\")\n", "            print(f\"🔧 Element Filtering: {'Enabled' if result['requires_filtering'] else 'Disabled'}\")\n", "            print(f\"\\n📝 Instructions:\")\n", "            print(result['instructions'])\n", "        else:\n", "            system_processor._reset_to_default()\n", "            print(\"🔄 System message cleared. Using default analysis mode.\")\n", "\n", "set_system_button.on_click(on_set_system_click)\n", "\n", "# Display system message interface\n", "display(widgets.VBox([\n", "    widgets.HTML(\"<h3>🎯 System Message Configuration</h3>\"),\n", "    widgets.HTML(\"<p>Set a system message to guide the analysis workflow. This enables specialized analysis modes like heuristic evaluation.</p>\"),\n", "    predefined_dropdown,\n", "    system_message_input,\n", "    set_system_button,\n", "    system_output\n", "]))\n", "\n", "# Set default heuristic evaluation message\n", "system_message_input.value = predefined_messages[\"Heuristic Evaluation\"]\n", "print(\"✅ System message interface ready!\")"]}, {"cell_type": "markdown", "metadata": {"id": "testing_examples"}, "source": ["## 🧪 Testing Examples with System Message Support\n", "\n", "### 🎯 System Message Examples:\n", "\n", "#### **Heuristic Evaluation Mode:**\n", "1. Set system message: `\"You need to perform a heuristic evaluation of this web page. This is a landing page.\"`\n", "2. Query: `\"Apply evaluation on this webpage.\"`\n", "   - 🔍 **Result**: Comprehensive heuristic evaluation report\n", "   - ✅ **Filters**: Excludes \"Non-UI Element\" and \"Unknown Element\"\n", "   - 📊 **Output**: Element-by-element violation analysis with severity levels\n", "\n", "#### **Accessibility Assessment Mode:**\n", "1. Set system message: `\"Perform accessibility assessment focusing on WCAG compliance.\"`\n", "2. Query: `\"Check accessibility compliance.\"`\n", "   - 🔍 **Result**: WCAG compliance assessment\n", "   - ✅ **Focus**: Screen reader compatibility, contrast, keyboard navigation\n", "\n", "#### **Design Review Mode:**\n", "1. Set system message: `\"Conduct design review focusing on visual hierarchy and typography.\"`\n", "2. Query: `\"Review the design of this page.\"`\n", "   - 🔍 **Result**: Visual design analysis\n", "   - ✅ **Focus**: Layout, typography, aesthetic principles\n", "\n", "### 🔍 Regular Analysis Examples (without system message):\n", "\n", "1. **\"What is the video element on the page?\"**\n", "   - 🎯 Target detected: 'video'\n", "   - Tests intelligent video element identification\n", "\n", "2. **\"Where is the main heading shown on the page?\"**\n", "   - 🎯 Target detected: 'heading'\n", "   - Tests position-aware responses for text elements\n", "\n", "3. **\"Tell me about the figure element and its location\"**\n", "   - 🎯 Target detected: 'figure'\n", "   - Tests comprehensive element analysis\n", "\n", "4. **\"What are the video attributes and properties?\"**\n", "   - 🎯 Target detected: 'video'\n", "   - Tests detailed DOM attribute extraction\n", "\n", "5. **\"Show me the CSS classes for the heading element\"**\n", "   - 🎯 Target detected: 'heading'\n", "   - Tests CSS class and styling information\n", "\n", "6. **\"Find the button element\"**\n", "   - 🎯 Target detected: 'button'\n", "   - Tests intelligent element finding\n", "\n", "7. **\"video properties and details\"**\n", "   - 🎯 Target detected: 'video'\n", "   - Tests pattern recognition without 'element' keyword\n", "\n", "### 🧠 Intelligent Label Detection Workflow:\n", "\n", "Each query follows this enhanced workflow:\n", "1. **🎯 Target Identification**: Specific UI element identified from query patterns\n", "2. **📍 Precise Search**: Search for exact target label in coordinates DB\n", "3. **🔍 Smart Matching**: Handle label variations and synonyms\n", "4. **📊 Index Extraction**: Extract indices for target elements only\n", "5. **🏗️ DOM Retrieval**: Retrieve complete data for target elements\n", "6. **🔄 Context Injection**: Combine target-specific information\n", "7. **🤖 Position-Aware Response**: Generate focused, accurate answers\n", "\n", "### 🎯 Benefits of Intelligent Detection:\n", "- ✅ **Precision**: Returns only the specific element the user asks about\n", "- ✅ **Accuracy**: 87.5% success rate in target identification\n", "- ✅ **Efficiency**: Reduces noise by focusing on target elements\n", "- ✅ **Intelligence**: Understands user intent from natural language\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {"id": "file_upload"}, "source": ["## 📁 Upload Your Own Data\n", "\n", "You can upload your own UI data files to analyze custom web pages!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "upload_interface"}, "outputs": [], "source": ["from google.colab import files\n", "import json\n", "\n", "def upload_custom_data():\n", "    \"\"\"Upload and process custom UI data files\"\"\"\n", "    \n", "    print(\"📁 Upload Your Custom UI Data Files\")\n", "    print(\"=\" * 50)\n", "    print(\"Please upload the following files:\")\n", "    print(\"1. coordinates.json - UI element positions and labels\")\n", "    print(\"2. element_info.json - Complete DOM data for elements\")\n", "    print(\"\\nNote: Files should follow the same format as the sample data.\")\n", "    \n", "    # Upload coordinates file\n", "    print(\"\\n📍 Upload coordinates.json:\")\n", "    coord_files = files.upload()\n", "    \n", "    # Upload element info file\n", "    print(\"\\n🏗️ Upload element_info.json:\")\n", "    element_files = files.upload()\n", "    \n", "    try:\n", "        # Process uploaded files\n", "        coord_filename = list(coord_files.keys())[0]\n", "        element_filename = list(element_files.keys())[0]\n", "        \n", "        # Load the data\n", "        with open(coord_filename, 'r') as f:\n", "            custom_coordinates = json.load(f)\n", "        \n", "        with open(element_filename, 'r') as f:\n", "            custom_element_info = json.load(f)\n", "        \n", "        print(f\"\\n✅ Files loaded successfully!\")\n", "        print(f\"📊 Coordinates: {len(custom_coordinates)} elements\")\n", "        print(f\"🏗️ Element info: {len(custom_element_info)} elements\")\n", "        \n", "        # Initialize analyzer with custom data\n", "        print(\"\\n🔄 Initializing analyzer with your data...\")\n", "        analyzer.initialize_data(custom_coordinates, custom_element_info)\n", "        \n", "        print(\"\\n🎉 Your custom data is now ready for analysis!\")\n", "        print(\"Use the query interface above to ask questions about your UI elements.\")\n", "        \n", "        return True\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error processing uploaded files: {str(e)}\")\n", "        print(\"Please check that your files are in the correct JSON format.\")\n", "        return False\n", "\n", "# Create upload button\n", "upload_button = widgets.Button(\n", "    description='📁 Upload Custom Data',\n", "    button_style='info',\n", "    layout=widgets.Layout(width='200px')\n", ")\n", "\n", "upload_output = widgets.Output()\n", "\n", "def on_upload_click(button):\n", "    with upload_output:\n", "        clear_output(wait=True)\n", "        upload_custom_data()\n", "\n", "upload_button.on_click(on_upload_click)\n", "\n", "display(widgets.VBox([\n", "    widgets.HTML(\"<h3>📁 Custom Data Upload</h3>\"),\n", "    widgets.HTML(\"<p>Upload your own coordinates.json and element_info.json files to analyze custom web pages.</p>\"),\n", "    upload_button,\n", "    upload_output\n", "]))"]}], "metadata": {"colab": {"provenance": [], "toc_visible": true}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}