# 🎯 System Message Functionality Guide

## Overview

The UI Element Analyzer now supports **System Messages** - a powerful feature that allows you to define analysis workflows and guide how the system processes user queries. This enables specialized analysis modes like heuristic evaluation, accessibility assessment, and design review.

## 🚀 Key Features

### ✅ **System Message Support**
- Define analysis workflows through natural language system messages
- Context-aware query processing based on system instructions
- Dynamic evaluation workflows

### ✅ **Heuristic Evaluation Engine**
- Comprehensive usability evaluation against established principles
- Element-by-element evaluation with violation detection
- Structured reporting with severity levels and recommendations

### ✅ **Element Filtering**
- Automatically exclude "Non-UI Element" and "Unknown Element" from evaluation
- Focus analysis on relevant UI components only

### ✅ **Structured Reporting**
- Detailed violation reports with specific heuristics
- Severity classification (high/medium/low)
- Actionable recommendations for improvements

## 🎯 How It Works

### 1. **System Message Processing**
The system analyzes your message to determine the appropriate workflow:

```python
# Example: Set heuristic evaluation mode
analyzer.set_system_message("You need to perform a heuristic evaluation of this web page. This is a landing page.")
```

### 2. **Workflow Detection**
Based on keywords in your message, the system activates specific workflows:

- **Heuristic Evaluation**: Keywords like "heuristic", "evaluation", "usability"
- **Accessibility Assessment**: Keywords like "accessibility", "a11y", "wcag"
- **Design Review**: Keywords like "design", "visual", "aesthetic"

### 3. **Element Processing**
For heuristic evaluation mode:
1. Filter out non-UI elements
2. Evaluate each remaining element against usability heuristics
3. Generate violation reports with severity levels
4. Provide actionable recommendations

## 📋 Usage Examples

### **Heuristic Evaluation**

```python
# Set system message
analyzer.set_system_message("You need to perform a heuristic evaluation of this web page. This is a landing page.")

# Run evaluation
result = analyzer.analyze_query("Apply evaluation on this webpage.")
```

**Expected Output:**
```
# 🔍 HEURISTIC EVALUATION REPORT

## 📊 EVALUATION SUMMARY
- Total Elements Evaluated: 3
- Elements with Violations: 1
- Total Violations Found: 3
- High Severity Issues: 1
- Medium Severity Issues: 2

## 🚨 VIOLATIONS BY ELEMENT

### Element 1: Video
Location: (949, 385)
✅ No heuristic violations found for this element.

### Element 2: Primary Button
Location: (323, 451)
Violations Found:
- 🔴 Recognition Rather Than Recall
  - Violation: Button without visible text label
  - Reason: Button element lacks descriptive text, users must recall its function
  - Severity: High
```

### **Accessibility Assessment**

```python
analyzer.set_system_message("Perform accessibility assessment focusing on WCAG compliance.")
result = analyzer.analyze_query("Check accessibility compliance.")
```

### **Design Review**

```python
analyzer.set_system_message("Conduct design review focusing on visual hierarchy and typography.")
result = analyzer.analyze_query("Review the design of this page.")
```

## 🔧 Configuration

### **Excluded Labels**
Elements with these labels are automatically excluded from heuristic evaluation:
- "Non-UI Element"
- "Unknown Element"

### **Supported Heuristics**
The system evaluates against these usability principles:
1. **Visibility of System Status**
2. **Recognition Rather Than Recall**
3. **Aesthetic and Minimalist Design**
4. **Consistency and Standards**
5. **Error Prevention**

## 🛠️ API Reference

### **UIAnalyzer Methods**

#### `set_system_message(message: str) -> Dict[str, Any]`
Set a system message to guide analysis workflow.

**Parameters:**
- `message`: System message defining the analysis context

**Returns:**
- Dictionary with workflow type, instructions, and filtering requirements

#### `perform_heuristic_evaluation(query: str = "Apply heuristic evaluation on this webpage.") -> str`
Convenience method to perform heuristic evaluation.

**Parameters:**
- `query`: Optional query for the evaluation

**Returns:**
- Formatted heuristic evaluation report

#### `get_system_context() -> Dict[str, Any]`
Get current system message context.

**Returns:**
- Current system message, workflow type, and filtering status

### **SystemMessageProcessor**

#### `set_system_message(message: str) -> Dict[str, Any]`
Parse and set system message.

#### `is_heuristic_evaluation_mode() -> bool`
Check if currently in heuristic evaluation mode.

### **HeuristicEvaluator**

#### `should_evaluate_element(coordinate_data: Dict) -> bool`
Determine if element should be evaluated based on label.

#### `evaluate_element(element_data: Dict, coordinate_data: Dict) -> Dict`
Evaluate single element against all heuristics.

## 🧪 Testing

Run the test suite to verify functionality:

```bash
python test_system_message.py
```

## 📊 Colab Integration

The functionality is fully integrated into the Colab notebook with:
- Interactive system message interface
- Predefined message templates
- Real-time workflow switching
- Visual feedback and progress tracking

## 🎯 Best Practices

1. **Clear System Messages**: Use specific, descriptive system messages
2. **Appropriate Workflows**: Choose the right analysis type for your needs
3. **Element Filtering**: Ensure your data has proper element labels
4. **Iterative Analysis**: Use different system messages for comprehensive evaluation

## 🔮 Future Enhancements

- Additional heuristic checks
- Custom evaluation criteria
- Batch processing capabilities
- Export functionality for reports
- Integration with design tools
