"""
Gemini-Powered Heuristic Evaluator for UI Elements

This module provides comprehensive heuristic evaluation capabilities using Gemini AI model
to evaluate each UI element individually against established usability principles.
"""

from typing import Dict, List, Any, Optional, Tuple
import json
import re
import google.generativeai as genai
from config import Config


class HeuristicEvaluator:
    """
    Gemini-Powered Heuristic Evaluator for UI Elements

    Uses Gemini AI model to evaluate each UI element individually against established usability principles:
    - Visibility of system status
    - Match between system and real world
    - User control and freedom
    - Consistency and standards
    - Error prevention
    - Recognition rather than recall
    - Flexibility and efficiency of use
    - Aesthetic and minimalist design
    - Help users recognize, diagnose, and recover from errors
    - Help and documentation
    """

    def __init__(self):
        self.config = Config()

        # Initialize Gemini model
        genai.configure(api_key=self.config.GOOGLE_API_KEY)
        self.model = genai.GenerativeModel(self.config.MODEL_NAME)

        # Define heuristic principles for Gemini evaluation
        self.heuristic_principles = self._get_heuristic_principles()

    def _get_heuristic_principles(self) -> str:
        """Get comprehensive heuristic evaluation principles for Gemini"""
        return """
        NIELSEN'S 10 USABILITY HEURISTICS FOR UI EVALUATION:

        1. VISIBILITY OF SYSTEM STATUS
        - The system should always keep users informed about what is going on
        - Provide appropriate feedback within reasonable time
        - Show loading states, progress indicators, current page/state
        - Interactive elements should provide visual feedback (hover, focus, active states)

        2. MATCH BETWEEN SYSTEM AND REAL WORLD
        - The system should speak the users' language
        - Use words, phrases and concepts familiar to the user
        - Follow real-world conventions
        - Make information appear in natural and logical order

        3. USER CONTROL AND FREEDOM
        - Users often choose system functions by mistake
        - Provide clearly marked "emergency exit" to leave unwanted state
        - Support undo and redo
        - Give users control over their experience

        4. CONSISTENCY AND STANDARDS
        - Users should not have to wonder whether different words, situations, or actions mean the same thing
        - Follow platform conventions and established design patterns
        - Maintain internal consistency throughout the interface

        5. ERROR PREVENTION
        - Even better than good error messages is a careful design that prevents problems from occurring
        - Eliminate error-prone conditions
        - Present users with confirmation options before committing to important actions

        6. RECOGNITION RATHER THAN RECALL
        - Minimize the user's memory load
        - Make objects, actions, and options visible
        - User should not have to remember information from one part of the dialogue to another
        - Instructions for use should be visible or easily retrievable

        7. FLEXIBILITY AND EFFICIENCY OF USE
        - Accelerators may speed up interaction for expert users
        - Allow users to tailor frequent actions
        - Provide shortcuts and customization options

        8. AESTHETIC AND MINIMALIST DESIGN
        - Dialogues should not contain information that is irrelevant or rarely needed
        - Every extra unit of information competes with relevant units of information
        - Keep interfaces clean and focused

        9. HELP USERS RECOGNIZE, DIAGNOSE, AND RECOVER FROM ERRORS
        - Error messages should be expressed in plain language
        - Precisely indicate the problem
        - Constructively suggest a solution

        10. HELP AND DOCUMENTATION
        - Even though it's better if the system can be used without documentation
        - Provide help and documentation when needed
        - Information should be easy to search and focused on user's task
        """

    def evaluate_element(self, element_data: Dict[str, Any], coordinate_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Evaluate a single UI element using Gemini AI against all heuristics

        Args:
            element_data: Complete DOM element information
            coordinate_data: Coordinate and label information

        Returns:
            Dictionary containing evaluation results from Gemini AI
        """
        # Prepare element information for Gemini
        element_info = {
            "index": coordinate_data.get("index", -1),
            "label": coordinate_data.get("label", "Unknown"),
            "coordinates": coordinate_data.get("coordinates", {}),
            "tag": element_data.get("tag", "unknown"),
            "text": element_data.get("text", ""),
            "css_selector": element_data.get("cssSelector", ""),
            "xpath": element_data.get("xpath", ""),
            "computed_style": element_data.get("computedStyle", {}),
            "attributes": element_data.get("attributes", {})
        }

        # Create comprehensive prompt for Gemini evaluation
        prompt = self._create_evaluation_prompt(element_info)

        try:
            # Get Gemini evaluation
            response = self.model.generate_content(prompt)

            # Parse Gemini response into structured format
            evaluation_result = self._parse_gemini_response(response.text, element_info)

            return evaluation_result

        except Exception as e:
            # Fallback evaluation result in case of error
            return {
                "element_info": element_info,
                "violations": [],
                "passed_checks": [],
                "overall_score": 0,
                "recommendations": [],
                "gemini_analysis": f"Error in Gemini evaluation: {str(e)}",
                "evaluation_status": "error"
            }

    def _create_evaluation_prompt(self, element_info: Dict[str, Any]) -> str:
        """Create comprehensive evaluation prompt for Gemini"""

        element_json = json.dumps(element_info, indent=2)

        prompt = f"""
You are a UX expert conducting a comprehensive heuristic evaluation of a UI element.

HEURISTIC EVALUATION PRINCIPLES:
{self.heuristic_principles}

ELEMENT TO EVALUATE:
{element_json}

EVALUATION TASK:
Analyze this UI element against ALL 10 Nielsen's usability heuristics. For each heuristic, determine if there are any violations.

RESPONSE FORMAT (JSON):
{{
    "violations": [
        {{
            "heuristic": "Heuristic Name",
            "violation": "Brief description of violation",
            "reason": "Detailed explanation of why this is a violation",
            "severity": "high|medium|low",
            "recommendation": "Specific actionable recommendation to fix this issue"
        }}
    ],
    "passed_checks": [
        "List of heuristic names that this element passes"
    ],
    "overall_score": 85,
    "summary": "Brief overall assessment of the element's usability",
    "key_recommendations": [
        "Most important recommendations for improvement"
    ]
}}

EVALUATION GUIDELINES:
1. Be thorough - check ALL 10 heuristics
2. Consider the element's context, purpose, and user expectations
3. Look at technical aspects: size, positioning, styling, text content
4. Consider accessibility and usability best practices
5. Provide specific, actionable recommendations
6. Rate severity based on impact on user experience
7. If no violations found for a heuristic, add it to passed_checks

IMPORTANT: Return ONLY valid JSON. No additional text or explanations outside the JSON.
"""
        return prompt

    def _parse_gemini_response(self, response_text: str, element_info: Dict[str, Any]) -> Dict[str, Any]:
        """Parse Gemini's JSON response into structured evaluation result"""
        try:
            # Try to extract JSON from response
            response_text = response_text.strip()

            # Remove any markdown code blocks if present
            if response_text.startswith("```json"):
                response_text = response_text[7:]
            if response_text.startswith("```"):
                response_text = response_text[3:]
            if response_text.endswith("```"):
                response_text = response_text[:-3]

            # Parse JSON response
            gemini_result = json.loads(response_text.strip())

            # Structure the result according to our format
            evaluation_result = {
                "element_info": element_info,
                "violations": gemini_result.get("violations", []),
                "passed_checks": gemini_result.get("passed_checks", []),
                "overall_score": gemini_result.get("overall_score", 0),
                "recommendations": gemini_result.get("key_recommendations", []),
                "gemini_analysis": gemini_result.get("summary", ""),
                "evaluation_status": "success"
            }

            return evaluation_result

        except json.JSONDecodeError as e:
            # Fallback: try to extract information from text response
            return self._parse_text_response(response_text, element_info)
        except Exception as e:
            # Error fallback
            return {
                "element_info": element_info,
                "violations": [],
                "passed_checks": [],
                "overall_score": 0,
                "recommendations": [],
                "gemini_analysis": f"Error parsing Gemini response: {str(e)}",
                "evaluation_status": "parse_error"
            }

    def _parse_text_response(self, response_text: str, element_info: Dict[str, Any]) -> Dict[str, Any]:
        """Fallback parser for non-JSON responses from Gemini"""
        try:
            # Try to extract key information from text response
            violations = []
            passed_checks = []
            recommendations = []

            # Look for violation patterns in text
            lines = response_text.split('\n')
            current_violation = {}

            for line in lines:
                line = line.strip()
                if 'violation' in line.lower() or 'issue' in line.lower():
                    if current_violation:
                        violations.append(current_violation)
                    current_violation = {
                        "heuristic": "General Usability",
                        "violation": line,
                        "reason": "Identified by Gemini analysis",
                        "severity": "medium"
                    }
                elif 'recommendation' in line.lower() or 'suggest' in line.lower():
                    recommendations.append(line)

            if current_violation:
                violations.append(current_violation)

            # Estimate score based on violations found
            score = max(0, 100 - (len(violations) * 15))

            return {
                "element_info": element_info,
                "violations": violations,
                "passed_checks": passed_checks,
                "overall_score": score,
                "recommendations": recommendations,
                "gemini_analysis": response_text,
                "evaluation_status": "text_parsed"
            }

        except Exception as e:
            return {
                "element_info": element_info,
                "violations": [],
                "passed_checks": [],
                "overall_score": 0,
                "recommendations": [],
                "gemini_analysis": f"Error in text parsing: {str(e)}",
                "evaluation_status": "text_parse_error"
            }

    def should_evaluate_element(self, coordinate_data: Dict[str, Any]) -> bool:
        """
        Determine if an element should be evaluated based on its label

        Args:
            coordinate_data: Coordinate and label information

        Returns:
            Boolean indicating whether to evaluate the element
        """
        label = coordinate_data.get("label", "")
        return label not in self.config.EXCLUDED_LABELS
