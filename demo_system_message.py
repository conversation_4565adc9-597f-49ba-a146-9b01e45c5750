#!/usr/bin/env python3
"""
Demo script for system message functionality

This script demonstrates the new system message and heuristic evaluation features
with sample data to show how the system works.
"""

import json
from system_message_processor import SystemMessageProcessor
from heuristic_evaluator import HeuristicEvaluator


def create_sample_data():
    """Create sample UI element data for demonstration"""
    
    # Sample coordinate data
    coordinates_data = [
        {
            "index": 0,
            "label": "Video",
            "coordinates": {"x": 949, "y": 385, "width": 300, "height": 200}
        },
        {
            "index": 1,
            "label": "Primary Button",
            "coordinates": {"x": 323, "y": 451, "width": 30, "height": 20}  # Too small
        },
        {
            "index": 2,
            "label": "Main Heading",
            "coordinates": {"x": 100, "y": 50, "width": 400, "height": 60}
        },
        {
            "index": 3,
            "label": "Non-UI Element",  # Should be filtered out
            "coordinates": {"x": 0, "y": 0, "width": 10, "height": 10}
        },
        {
            "index": 4,
            "label": "Secondary Button",
            "coordinates": {"x": 400, "y": 500, "width": 100, "height": 40}
        }
    ]
    
    # Sample element info data
    element_info_data = {
        "element_1": {
            "tag": "video",
            "text": "",
            "cssSelector": "video.hero-video",
            "xpath": "//video[1]",
            "computedStyle": {"cursor": "default"}
        },
        "element_2": {
            "tag": "button",
            "text": "",  # No text - violation
            "cssSelector": "button.cta",
            "xpath": "//button[1]",
            "computedStyle": {"cursor": "default"}  # No pointer - violation
        },
        "element_3": {
            "tag": "h1",
            "text": "Welcome to Our Amazing Product",
            "cssSelector": "h1.hero-title",
            "xpath": "//h1[1]",
            "computedStyle": {"cursor": "default"}
        },
        "element_4": {
            "tag": "div",
            "text": "",
            "cssSelector": "div.spacer",
            "xpath": "//div[1]",
            "computedStyle": {"cursor": "default"}
        },
        "element_5": {
            "tag": "button",
            "text": "Learn More",
            "cssSelector": "button.secondary",
            "xpath": "//button[2]",
            "computedStyle": {"cursor": "pointer"}
        }
    }
    
    return coordinates_data, element_info_data


def demo_system_message_processing():
    """Demonstrate system message processing"""
    print("🎯 SYSTEM MESSAGE PROCESSING DEMO")
    print("=" * 60)
    
    processor = SystemMessageProcessor()
    
    # Demo different system messages
    test_messages = [
        "You need to perform a heuristic evaluation of this web page. This is a landing page.",
        "Perform accessibility assessment focusing on WCAG compliance and screen reader compatibility.",
        "Conduct design review focusing on visual hierarchy and typography.",
        "Just analyze the UI elements normally."
    ]
    
    for i, message in enumerate(test_messages, 1):
        print(f"\n📋 Test {i}: {message}")
        print("-" * 50)
        
        result = processor.set_system_message(message)
        
        print(f"🔧 Workflow Type: {result['workflow_type'].replace('_', ' ').title()}")
        print(f"🔍 Element Filtering: {'Enabled' if result['requires_filtering'] else 'Disabled'}")
        print(f"📝 Instructions Preview: {result['instructions'][:80]}...")


def demo_heuristic_evaluation():
    """Demonstrate heuristic evaluation with sample data"""
    print("\n\n🔍 HEURISTIC EVALUATION DEMO")
    print("=" * 60)
    
    evaluator = HeuristicEvaluator()
    coordinates_data, element_info_data = create_sample_data()
    
    print("📊 Sample Data Overview:")
    print(f"- Total elements: {len(coordinates_data)}")
    print(f"- Element types: {[item['label'] for item in coordinates_data]}")
    
    # Filter elements for evaluation
    evaluable_elements = []
    for coord_data in coordinates_data:
        if evaluator.should_evaluate_element(coord_data):
            evaluable_elements.append(coord_data)
            print(f"✅ Will evaluate: {coord_data['label']}")
        else:
            print(f"⏭️ Will skip: {coord_data['label']} (excluded)")
    
    print(f"\n📋 Evaluating {len(evaluable_elements)} elements...")
    print("=" * 60)
    
    # Perform evaluation on each element
    evaluation_results = []
    for coord_data in evaluable_elements:
        element_key = f"element_{coord_data['index'] + 1}"
        if element_key in element_info_data:
            element_data = element_info_data[element_key]
            
            print(f"\n🔍 Evaluating: {coord_data['label']}")
            print(f"   📍 Location: ({coord_data['coordinates']['x']}, {coord_data['coordinates']['y']})")
            print(f"   📏 Size: {coord_data['coordinates']['width']}×{coord_data['coordinates']['height']}px")
            print(f"   🏷️ Tag: {element_data['tag']}")
            
            evaluation = evaluator.evaluate_element(element_data, coord_data)
            evaluation_results.append(evaluation)
            
            if evaluation['violations']:
                print(f"   🚨 Violations: {len(evaluation['violations'])}")
                for violation in evaluation['violations']:
                    severity_emoji = "🔴" if violation['severity'] == 'high' else "🟡" if violation['severity'] == 'medium' else "🟢"
                    print(f"      {severity_emoji} {violation['heuristic']}: {violation['violation']}")
            else:
                print(f"   ✅ No violations found")
            
            print(f"   📊 Score: {evaluation['overall_score']:.1f}%")
    
    # Generate summary
    print(f"\n📊 EVALUATION SUMMARY")
    print("=" * 60)
    
    total_elements = len(evaluation_results)
    elements_with_violations = sum(1 for result in evaluation_results if result['violations'])
    total_violations = sum(len(result['violations']) for result in evaluation_results)
    
    high_severity = sum(1 for result in evaluation_results 
                       for violation in result['violations'] 
                       if violation.get('severity') == 'high')
    
    medium_severity = sum(1 for result in evaluation_results 
                         for violation in result['violations'] 
                         if violation.get('severity') == 'medium')
    
    low_severity = sum(1 for result in evaluation_results 
                      for violation in result['violations'] 
                      if violation.get('severity') == 'low')
    
    print(f"📈 Total Elements Evaluated: {total_elements}")
    print(f"🚨 Elements with Violations: {elements_with_violations}")
    print(f"⚠️ Total Violations Found: {total_violations}")
    print(f"🔴 High Severity Issues: {high_severity}")
    print(f"🟡 Medium Severity Issues: {medium_severity}")
    print(f"🟢 Low Severity Issues: {low_severity}")
    
    # Calculate overall page score
    if evaluation_results:
        avg_score = sum(result['overall_score'] for result in evaluation_results) / len(evaluation_results)
        print(f"📊 Overall Page Score: {avg_score:.1f}%")
    
    return evaluation_results


def demo_integration():
    """Demonstrate full integration workflow"""
    print("\n\n🔄 INTEGRATION WORKFLOW DEMO")
    print("=" * 60)
    
    processor = SystemMessageProcessor()
    evaluator = HeuristicEvaluator()
    coordinates_data, element_info_data = create_sample_data()
    
    # Step 1: Set system message
    print("🎯 Step 1: Setting System Message")
    system_message = "You need to perform a heuristic evaluation of this web page. This is a landing page."
    result = processor.set_system_message(system_message)
    print(f"✅ System message set: {result['workflow_type']} mode")
    
    # Step 2: Check if heuristic evaluation mode
    print(f"\n🔍 Step 2: Checking Evaluation Mode")
    is_heuristic_mode = processor.is_heuristic_evaluation_mode()
    print(f"✅ Heuristic evaluation mode: {is_heuristic_mode}")
    
    # Step 3: Process elements with filtering
    print(f"\n🔧 Step 3: Processing Elements with Filtering")
    if is_heuristic_mode:
        print("🔍 Performing heuristic evaluation workflow...")
        
        # Filter and evaluate elements
        evaluation_count = 0
        for coord_data in coordinates_data:
            if evaluator.should_evaluate_element(coord_data):
                evaluation_count += 1
                print(f"   ✅ Processing: {coord_data['label']}")
            else:
                print(f"   ⏭️ Skipping: {coord_data['label']} (filtered)")
        
        print(f"📊 Total elements processed: {evaluation_count}/{len(coordinates_data)}")
    
    # Step 4: Generate mock AI response
    print(f"\n🤖 Step 4: AI Response Generation")
    context = processor.get_current_context()
    print(f"✅ System context available: {bool(context['system_message'])}")
    print(f"✅ Workflow instructions: {len(result['instructions'])} characters")
    print(f"✅ Element filtering: {context['requires_filtering']}")
    
    print("\n🎉 Integration workflow completed successfully!")


def main():
    """Run the complete demo"""
    print("🚀 SYSTEM MESSAGE FUNCTIONALITY DEMO")
    print("=" * 80)
    print("This demo showcases the new system message and heuristic evaluation features.")
    print("=" * 80)
    
    try:
        # Demo 1: System message processing
        demo_system_message_processing()
        
        # Demo 2: Heuristic evaluation
        demo_heuristic_evaluation()
        
        # Demo 3: Integration workflow
        demo_integration()
        
        print("\n" + "=" * 80)
        print("🎉 DEMO COMPLETED SUCCESSFULLY!")
        print("\n📋 What you've seen:")
        print("✅ System message parsing and workflow detection")
        print("✅ Element filtering based on labels")
        print("✅ Comprehensive heuristic evaluation")
        print("✅ Violation detection with severity levels")
        print("✅ Structured reporting and recommendations")
        print("✅ Full integration workflow")
        print("\n🚀 Ready to use in your UI analysis projects!")
        
    except Exception as e:
        print(f"\n❌ Demo failed with error: {str(e)}")
        raise


if __name__ == "__main__":
    main()
