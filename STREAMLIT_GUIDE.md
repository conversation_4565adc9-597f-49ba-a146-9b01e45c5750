# 🎯 Streamlit App with System Message Functionality

## 🚀 Quick Start

### 1. **Install Dependencies**
```bash
pip install streamlit
```

### 2. **Set Environment Variables**
Create a `.env` file with your Google API key:
```
GOOGLE_API_KEY=your_gemini_api_key_here
```

### 3. **Run the Streamlit App**
```bash
streamlit run streamlit_app.py
```

The app will open in your browser at `http://localhost:8501`

## 🎯 New System Message Features

### **Enhanced Interface**
- **System Message Panel**: Set analysis workflows through natural language
- **Dynamic Query Examples**: Context-aware example queries based on active mode
- **Quick Actions**: One-click heuristic evaluation
- **Report Downloads**: Export evaluation results as Markdown files

### **Supported Workflows**

#### 🔍 **Heuristic Evaluation Mode**
- **System Message**: "You need to perform a heuristic evaluation of this web page. This is a landing page."
- **Features**:
  - Element-by-element usability evaluation
  - Violation detection with severity levels
  - Structured reports with recommendations
  - Automatic filtering of non-UI elements

#### ♿ **Accessibility Assessment Mode**
- **System Message**: "Perform accessibility assessment focusing on WCAG compliance and screen reader compatibility."
- **Features**:
  - WCAG compliance evaluation
  - Screen reader compatibility assessment
  - Accessibility violation reporting

#### 🎨 **Design Review Mode**
- **System Message**: "Conduct design review focusing on visual hierarchy, typography, and aesthetic principles."
- **Features**:
  - Visual design analysis
  - Typography and layout evaluation
  - Aesthetic principle assessment

## 📋 How to Use

### **Step 1: Load Data**
1. **Use Default Files**: Click "Load Default Data" to use sample files
2. **Upload Custom Files**: Upload your own screenshot, coordinates.json, and element_info.json

### **Step 2: Set System Message**
1. **Quick Select**: Choose from predefined templates
2. **Custom Message**: Write your own system message
3. **Click "Set System Message"** to activate the workflow

### **Step 3: Analyze**
1. **Quick Evaluation**: Click "⚡ Quick Heuristic Evaluation" for instant assessment
2. **Custom Queries**: Ask specific questions based on your active mode
3. **View Results**: Get detailed analysis with downloadable reports

## 🔧 Interface Components

### **Sidebar Features**
- **📁 Data Upload**: File management and initialization
- **🎯 System Message**: Workflow configuration
- **📊 Status**: Real-time status indicators

### **Main Interface**
- **📷 Screenshot Display**: Visual reference of the analyzed page
- **💬 Chat Interface**: Interactive query and response system
- **⚡ Quick Actions**: One-click evaluation and report generation

### **Enhanced Chat Features**
- **Context-Aware Responses**: Analysis guided by system message
- **Special Report Formatting**: Enhanced display for heuristic evaluations
- **Download Functionality**: Export reports as Markdown files
- **Chat History**: Persistent conversation tracking

## 🎯 Example Workflows

### **Heuristic Evaluation Workflow**
1. Set system message: "Heuristic Evaluation"
2. Click "⚡ Quick Heuristic Evaluation"
3. Review detailed violation report
4. Download report for documentation

### **Accessibility Review Workflow**
1. Set system message: "Accessibility Assessment"
2. Ask: "Check accessibility compliance of this page"
3. Review WCAG compliance analysis
4. Get actionable recommendations

### **General Analysis Workflow**
1. Keep default system message (or clear it)
2. Ask: "Where is the main heading located?"
3. Get coordinate and DOM information
4. Continue with follow-up questions

## 📊 Report Features

### **Heuristic Evaluation Reports**
- **Summary Statistics**: Total elements, violations, severity breakdown
- **Element-by-Element Analysis**: Detailed violation reports
- **Actionable Recommendations**: Specific improvement suggestions
- **Downloadable Format**: Markdown files for documentation

### **Report Structure**
```markdown
# 🔍 HEURISTIC EVALUATION REPORT

## 📊 EVALUATION SUMMARY
- Total Elements Evaluated: X
- Elements with Violations: Y
- High Severity Issues: Z

## 🚨 VIOLATIONS BY ELEMENT
[Detailed element analysis...]

## 🤖 AI ANALYSIS & RECOMMENDATIONS
[AI-generated insights and recommendations...]
```

## 🔧 Configuration

### **System Message Templates**
The app includes predefined templates for common workflows:
- **Heuristic Evaluation**: Usability assessment
- **Accessibility Assessment**: WCAG compliance
- **Design Review**: Visual design analysis
- **Usability Testing**: User experience evaluation

### **File Requirements**
- **Screenshot**: PNG/JPG image of the web page
- **Coordinates JSON**: UI element positions and labels
- **Element Info JSON**: DOM data for each element

### **Element Filtering**
In heuristic evaluation mode, elements with these labels are automatically excluded:
- "Non-UI Element"
- "Unknown Element"

## 🚨 Troubleshooting

### **Common Issues**

#### **API Key Error**
```
⚠️ Please set your GOOGLE_API_KEY in the .env file
```
**Solution**: Create `.env` file with valid Google API key

#### **Import Errors**
```
ModuleNotFoundError: No module named 'streamlit'
```
**Solution**: Install required dependencies
```bash
pip install streamlit sentence-transformers chromadb google-generativeai
```

#### **File Upload Issues**
**Solution**: Ensure files are in correct format:
- Screenshot: PNG/JPG image
- JSON files: Valid JSON structure

### **Performance Tips**
- Use default files for quick testing
- Clear chat history periodically for better performance
- Download reports before clearing history

## 🎉 Features Summary

### ✅ **Implemented Features**
- **System Message Interface**: Natural language workflow configuration
- **Heuristic Evaluation Engine**: Comprehensive usability assessment
- **Element Filtering**: Automatic exclusion of non-UI elements
- **Dynamic Query Examples**: Context-aware suggestions
- **Report Generation**: Structured evaluation reports
- **Download Functionality**: Export reports as Markdown
- **Quick Actions**: One-click evaluations
- **Session Management**: Persistent state across interactions

### 🔮 **Future Enhancements**
- Batch file processing
- Custom heuristic criteria
- Integration with design tools
- Advanced filtering options
- Multi-language support

## 📞 Support

For issues or questions:
1. Check the troubleshooting section
2. Review the system message examples
3. Verify file formats and API configuration
4. Test with default files first

---

**Ready to analyze UI elements with advanced system message functionality!** 🎯
